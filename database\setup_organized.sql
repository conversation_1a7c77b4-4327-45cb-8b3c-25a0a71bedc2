-- KMS PC Receipt Maker - Organized Database Setup
-- 組織化的數據庫設置腳本
-- 
-- 此腳本按功能組織數據庫結構：
-- 1. 核心表格 (Core Tables)
-- 2. 產品管理 (Product Management)  
-- 3. 配置表格 (Configuration Tables)
-- 4. 範例數據 (Sample Data)
--
-- 使用方法：
-- 1. 在 MySQL 中執行此腳本，或
-- 2. 分別執行各個功能模組的 SQL 文件

-- 執行核心表格創建
SOURCE 01_core_tables.sql;

-- 執行產品管理表格創建
SOURCE 02_product_management.sql;

-- 執行配置表格創建
SOURCE 03_configuration_tables.sql;

-- 執行範例數據插入
SOURCE 04_sample_data.sql;

-- 顯示創建完成的表格
SHOW TABLES;
