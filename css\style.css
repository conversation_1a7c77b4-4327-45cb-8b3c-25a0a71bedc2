/* KMS PC Receipt Maker - 優化後的主樣式文件 */

/* Unicode Icon Symbols - Replacing FontAwesome */
.icon-symbol {
    font-family: 'Segoe UI Emoji', 'Apple Color Emoji', 'Noto Color Emoji', sans-serif;
    font-style: normal;
    font-weight: normal;
    line-height: 1;
    display: inline-block;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Large icon styles for empty states */
.icon-symbol.large {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    display: block;
}

.icon-symbol.extra-large {
    font-size: 3rem;
    margin-bottom: 1rem;
    display: block;
}

/* 1. 全局與根樣式 */
:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --transition: all 0.15s ease-in-out;
}

* {
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #1b8e93;
    background: #0095ff;
    min-height: 100vh;
    position: relative;
}

body::before {
    content: '';
    position: fixed;
    top: 0; left: 0;
    width: 100%; height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.05"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.05"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.05"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
    z-index: -1;
}

/* 自定義滾動條 */
::-webkit-scrollbar { width: 8px; }
::-webkit-scrollbar-track { background: #f1f1f1; border-radius: 4px; }
::-webkit-scrollbar-thumb { background: #c1c1c1; border-radius: 4px; }
::-webkit-scrollbar-thumb:hover { background: #a8a8a8; }


/* 2. 佈局樣式 */
.kms-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 32px;
    min-height: 100vh;
}

.kms-main-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: 32px;
    padding: 32px 0;
}

.kms-content-wrapper {
    background: rgb(91, 139, 237);
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
}

/* 區域切換 */
.section { display: none; animation: fadeIn 0.3s ease-in-out; }
.section.active { display: block !important; }


/* 3. 導覽列 (Navbar) */
.kms-navbar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.kms-nav-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 16px 32px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.kms-brand {
    display: flex;
    align-items: center;
    gap: 12px;
    color: white;
    font-weight: 700;
    font-size: 24px;
    text-decoration: none;
}

.kms-brand-icon {
    font-size: 32px;
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.kms-brand-text {
    background: linear-gradient(45deg, #ffffff, #f0f8ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.kms-nav-menu {
    display: flex;
    align-items: center;
    gap: 16px;
}

.kms-nav-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    color: white;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.kms-nav-btn::before {
    content: '';
    position: absolute;
    top: 0; left: -100%;
    width: 100%; height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.kms-nav-btn:hover::before { left: 100%; }
.kms-nav-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.kms-nav-btn.active {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    color: #333;
    border-color: #ffd700;
    box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4);
}

.kms-nav-btn.active:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.6);
}

.kms-language-group {
    display: flex;
    gap: 8px;
    margin-left: 16px;
    padding-left: 16px;
    border-left: 2px solid rgba(255, 255, 255, 0.2);
}

.kms-lang-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    color: white;
    font-weight: 500;
    font-size: 14.5px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.kms-lang-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.kms-lang-btn.active {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
}

.kms-mobile-toggle {
    display: none;
    flex-direction: column;
    gap: 4px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
}

.kms-mobile-toggle span {
    width: 25px;
    height: 3px;
    background: white;
    border-radius: 2px;
    transition: all 0.3s ease;
}


/* 4. 通用元件 (Components) */

/* 卡片 (Card) */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    margin-bottom: 24px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.card-header {
    background: #ff6e00;
    border-bottom: none;
    border-radius: 15px 15px 0 0 !important;
    padding: 4px 10px;
}

.card-header h5, .card-header h6 { margin: 0; font-weight: 600; }
.card-title { font-weight: 600; color: #333; }
.card-body { padding: 4px; background-color: #ffb400; }

/* 表單 (Form) */
.form-control, .form-select {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
    padding: 4px 6px;
    margin-bottom: 12px;
    background: rgba(255, 255, 255, 0.9);
}

.form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3.2px rgba(102, 126, 234, 0.25);
    background: rgba(255, 255, 255, 1);
    transform: translateY(-1px);
}

.form-label {
    font-weight: 500;
    color: var(--dark-color);
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 14px;
}

/* 按鈕 (Button) */
.btn {
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    padding: 12px 24px;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 14px;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0; left: -100%;
    width: 100%; height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before { left: 100%; }
.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.btn-primary { background: #0095ff; box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4); }
.btn-success { background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%); box-shadow: 0 4px 15px rgba(17, 153, 142, 0.4); }
.btn-danger { background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%); box-shadow: 0 4px 15px rgba(255, 65, 108, 0.4); }
.btn-warning { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); box-shadow: 0 4px 15px rgba(240, 147, 251, 0.4); color: white; }
.btn-info { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); box-shadow: 0 4px 15px rgba(79, 172, 254, 0.4); color: white; }
.btn-secondary { background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%); box-shadow: 0 4px 15px rgba(108, 117, 125, 0.4); }

.btn-lg { font-size: 14px; border-radius: 50px; }
.btn-xs { padding: 2px 4px; font-size: 12px; line-height: 1.2; border-radius: 3.2px; min-width: 28px; height: 24px; }

.btn-group .btn { margin: 0 1px; }
.btn-group-sm .btn { padding: 4px 8px; font-size: 12.8px; }
.btn-group-vertical .btn-xs { margin-bottom: 2px; }
.btn-group-vertical .btn-xs:last-child { margin-bottom: 0; }

/* 徽章 (Badge) */
.badge {
    border-radius: 20px;
    padding: 8px 16px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 12px;
}

/* 訊息提示 (Alert) */
.alert {
    border-radius: 15px;
    border: none;
    font-weight: 500;
    backdrop-filter: blur(10px);
    animation: slideInDown 0.3s ease-out;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}
.alert-success { background: linear-gradient(135deg, #d1edff 0%, #a7d8f0 100%); color: #0c5460; }
.alert-danger { background: linear-gradient(135deg, #f8d7da 0%, #f1aeb5 100%); color: #721c24; }
.alert-warning { background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%); color: #856404; }
.alert-info { background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%); color: #0c5460; }

/* 模態框 (Modal) */
.modal {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    z-index: 1055 !important;
    width: 100% !important;
    height: 100% !important;
    overflow: hidden !important;
    outline: 0 !important;
}

.modal-content {
    border: none;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(20px);
    background: rgb(87, 159, 236);
}
.modal-header { border-bottom: 1px solid rgba(0, 0, 0, 0.1); border-radius: 20px 20px 0 0; padding: 24px; }
.modal-body { padding: 24px; max-height: 70vh; overflow-y: auto; }
.modal-footer { border-top: 1px solid rgba(0, 0, 0, 0.1); border-radius: 0 0 20px 20px; padding: 24px; }

/* 確保modal backdrop正確顯示 */
.modal-backdrop {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    z-index: 1040 !important;
    width: 100vw !important;
    height: 100vh !important;
    background-color: rgba(0, 0, 0, 0.5) !important;
}

/* 防止body滾動 */
body.modal-open {
    overflow: hidden !important;
}

/* 工具提示 (Tooltip) */
.tooltip { font-size: 14px; }

/* 載入狀態 (Loading) */
.loading {
    display: inline-block;
    width: 20px; height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}
.loading-overlay {
    position: fixed;
    top: 0; left: 0;
    width: 100%; height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}
.loading-spinner {
    width: 50px; height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}


/* 5. 特定區域/元件樣式 */

/* 通用互動項目基礎樣式 (合併重複樣式) */
.kms-interactive-item {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 2px solid transparent;
    border-radius: 15px;
    transition: all 0.3s ease;
}
.kms-interactive-item:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.2);
}

/* 項目列表 (繼承基礎樣式) */
.item-row {
    padding: 16px;
    margin-bottom: 16px;
    border-color: #e9ecef; /* 恢復原有的邊框色 */
}
.item-row .btn-group { flex-wrap: nowrap; }
.item-row .btn-group .btn { padding: 4px 8px; font-size: 14px; border-radius: 8px; margin: 0 2px; }

/* 收據項目 (繼承基礎樣式) */
.receipt-item-row {
    composes: kms-interactive-item; /* 邏輯繼承 */
}

/* 預設項目 (繼承基礎樣式) */
.preset-item {
    composes: kms-interactive-item;
    height: 60px;
    display: flex;
    align-items: center;
}
#presetList { background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%); border-radius: 10px; padding: 16px; }
.preset-item .btn-group { flex-wrap: wrap; gap: 4px; }
.preset-item .btn-group .btn { margin: 2px; }

/* 配置項目 (繼承基礎樣式) */
.configuration-item {
    composes: kms-interactive-item;
}

/* 舊版收據項目 */
.legacy-receipt-item {
    background-color: white;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 16px;
    margin-bottom: 16px;
    transition: var(--transition);
}
.legacy-receipt-item:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 4px rgba(13, 110, 253, 0.15);
}
.legacy-receipt-item .remove-item {
    position: absolute;
    top: 8px; right: 8px;
    background: var(--danger-color);
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px; height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    cursor: pointer;
    transition: var(--transition);
}
.legacy-receipt-item .remove-item:hover { background: #bb2d3b; transform: scale(1.1); }

/* 預覽區域 */
.receipt-preview {
    background: #f8f9fa;
    border: 2px dashed #dee2e6;
    border-radius: 15px;
    padding: 32px;
    min-height: 400px;
    transition: all 0.3s ease;
}
.receipt-preview.has-content {
    border-style: solid;
    border-color: #667eea;
    background: white;
}

/* 歷史記錄 */
.receipt-history-item {
    background-color: #ffb983;
    border: 1px solid #ffcd77bb;
    border-radius: 12px;
    margin-bottom: 2px;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
}
.receipt-history-item:hover {
    background-color: #00ffc3;
    border-color: #0dfdfd !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}
.receipt-history-item .btn { transition: all 0.2s ease-in-out; }
.receipt-history-item .btn:hover { transform: scale(1.05); }
.receipt-number { font-weight: 600; color: var(--primary-color); }
.receipt-amount { font-weight: 600; font-size: 17.6px; color: var(--success-color); }

/* 拖拽排序 */
.order-controls { display: flex; flex-direction: column; align-items: center; gap: 8px; }
.order-input { width: 50px; text-align: center; font-size: 12.8px; }
.drag-handle { cursor: grab; color: #6c757d; padding: 4px; border-radius: 4px; transition: all 0.3s ease; }
.drag-handle:hover { color: #667eea; background-color: rgba(102, 126, 234, 0.1); }
.drag-handle:active { cursor: grabbing; }
.preset-item[draggable="true"] { cursor: move; }
.preset-item.dragging { opacity: 0.5; transform: rotate(2deg); }
.preset-item.drag-over { border-top: 3px solid #667eea !important; transform: translateY(-2px); }

/* Logo 預覽 */
.logo-preview { animation: slideInUp 0.3s ease-out; }
.logo-preview .card { border: none; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1); border-radius: 10px; }
.logo-preview .card-header { background: #fcca03; color: white; border-radius: 10px 10px 0 0; }
#logoPreviewImage { border: 2px solid #e9ecef; border-radius: 8px; transition: all 0.3s ease; }
#logoPreviewImage:hover { border-color: #667eea; transform: scale(1.05); }

/* 特定模態框樣式 */
#presetModal .modal-dialog { min-width: 75%; min-height: 75%; }
#presetModal .modal-content { min-height: 75vh; background: linear-gradient(135deg, #20b2aa 0%, #008b8b 100%); }
#receiptDetailsModal .modal-dialog { max-width: 90%; }
#receiptDetailsModal .card { border: 1px solid #dee2e6; border-radius: 8px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.075); }
#receiptDetailsModal .card-header { background-color: #f8f9fa; border-bottom: 1px solid #dee2e6; font-weight: 600; }
#receiptDetailsModal .table { margin-bottom: 0; }
#receiptDetailsModal .table th { background-color: #f8f9fa; border-top: none; font-weight: 600; font-size: 14px; }
#receiptDetailsModal .table td { vertical-align: middle; font-size: 14px; }
#receiptDetailsModal .badge { font-size: 12px; }
#receiptDetailsModal .modal-body { max-height: 70vh; overflow-y: auto; }

/* 總計顯示 */
#totalsDisplay .card { border: none; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1); border-radius: 15px; }
#totalsDisplay .card-body { padding: 24px; }
#totalsDisplay .row { margin-bottom: 8px; align-items: center; }
#totalsDisplay hr { margin: 16px 0; border-top: 2px solid #667eea; }

/* 批次刪除 */
.batch-controls {
    border: 1px solid #dee2e6;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    animation: slideDown 0.3s ease-out;
}
.receipt-checkbox { transform: scale(1.2); margin-right: 8px; }
.receipt-checkbox:checked { background-color: #0d6efd; border-color: #0d6efd; }
.receipt-history-item:has(.receipt-checkbox:checked) {
    background-color: #e7f3ff;
    border-color: #0d6efd !important;
    box-shadow: 0 0 0 2px rgba(13, 110, 253, 0.1);
}
.btn-outline-danger:hover { transform: scale(1.05); box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3); }
.batch-controls .btn { transition: all 0.2s ease-in-out; }
.batch-controls .btn:hover { transform: translateY(-1px); box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); }
.btn-outline-info:hover, .btn-outline-warning:hover { transform: scale(1.05); }


/* 6. 動畫 (Animations) */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}
@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}
@keyframes slideInUp {
    from { opacity: 0; transform: translateY(30px); }
    to { opacity: 1; transform: translateY(0); }
}
@keyframes slideInDown {
    from { opacity: 0; transform: translateY(-20px); }
    to { opacity: 1; transform: translateY(0); }
}
@keyframes slideDown {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}
.container { animation: slideInUp 0.6s ease-out; }


/* 7. 響應式設計 (Responsive Design) */
@media (max-width: 768px) {
    .container { padding-left: 16px; padding-right: 16px; }
    .kms-nav-container { padding: 16px; }
    .kms-nav-menu {
        display: none;
        position: absolute;
        top: 100%; left: 0; right: 0;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        flex-direction: column;
        padding: 16px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    }
    .kms-nav-menu.active { display: flex; }
    .kms-mobile-toggle { display: flex; }
    .kms-language-group {
        margin-left: 0; padding-left: 0;
        border-left: none;
        border-top: 2px solid rgba(255, 255, 255, 0.2);
        padding-top: 16px; margin-top: 16px;
    }
    .kms-brand-text { font-size: 19.2px; }
    .card-body { padding: 16px; }
    .btn { margin-bottom: 8px; }
    .d-flex.gap-2.flex-wrap .btn { flex: 1 1 auto; min-width: 120px; }
    .legacy-receipt-item { padding: 12px; }
    .navbar-brand { font-size: 16px; }
    .form-group.mb-0 { margin-bottom: 8px !important; }
    .d-flex.gap-2.align-items-center.flex-wrap {
        flex-direction: column;
        align-items: stretch !important;
        gap: 8px !important;
    }
    .d-flex.gap-2.align-items-center.flex-wrap > * { margin-bottom: 8px; }
    .batch-controls { flex-direction: column; gap: 8px; }
    .batch-controls .d-flex { flex-direction: column; align-items: stretch !important; gap: 8px; }
    .receipt-history-item .col-md-1,
    .receipt-history-item .col-md-2 { flex: 0 0 auto; width: auto; }
}

@media (max-width: 576px) {
    .container { padding-left: 8px; padding-right: 8px; }
    .card { margin-bottom: 16px; }
    .receipt-preview { min-height: 300px; padding: 16px; }
}


/* Customer Info 區域樣式優化 */
.customer-info-section {
    background: #1cc1dc;
    border-radius: 15px;
    padding: 24px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.customer-info-section h3 {
    font-weight: 600;
    margin-bottom: 20px;
    color: #ffffff;
}

.customer-info-section .form-label {
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 8px;
    font-size: 16px;
}

.customer-info-section .form-control {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 12px 16px;
    font-size: 16px;
    transition: all 0.3s ease;
    background-color: #fff;
}

.customer-info-section .form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.15);
    background-color: #fff;
}

.customer-info-section .form-control::placeholder {
    color: #adb5bd;
    font-style: italic;
}

/* Modal 樣式增強 */
.modal.show {
    display: flex !important;
    align-items: center;
    justify-content: center;
}

.modal.show .modal-dialog {
    margin: 1.75rem auto;
    transform: none;
}

/* 確保 modal 在正確位置顯示 */
.modal {
    padding-left: 0 !important;
    padding-right: 0 !important;
}

/* 修復 modal 定位問題 */
.modal-dialog-centered {
    display: flex;
    align-items: center;
    min-height: calc(100vh - 1rem);
}

/* 確保 modal backdrop 正確顯示 */
.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1040;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.5);
}

/* 8. 列印樣式 (Print) */
@media print {
    #receiptDetailsModal .modal-header,
    #receiptDetailsModal .modal-footer {
        display: none !important;
    }
    #receiptDetailsModal .modal-body {
        max-height: none !important;
        overflow: visible !important;
    }
    #receiptDetailsModal .card {
        break-inside: avoid;
        margin-bottom: 16px;
    }
}