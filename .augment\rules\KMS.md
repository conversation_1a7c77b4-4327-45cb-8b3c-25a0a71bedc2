---
type: "always_apply"
---

所有生成的語法都不可以超過 600 行，否則會導致解析錯誤，超過的部分要切分。
所有生成的 CSS 語法，按照功能性分類，分為以下幾類：
1. 基礎樣式
2. 布局樣式
3. 交互樣式
4. 響應式樣式
顏色使用變數：
所有顏色都使用變數表示，例如：
```
:root {
  --color-1:rgb(0, 255, 234);
  --color-2:rgb(0, 136, 255);
  --color-3:rgb(255, 213, 0);
  --color-4:rgb(255, 0, 0);
  --color-5:rgb(0, 76, 255);
  --border-color-1:rgb(0, 0, 0);
  --border-color-2:rgb(255, 255, 255);
  --border-color-3:rgb(0, 255, 234);
  --border-color-4:rgb(0, 136, 255);
  --border-color-5:rgb(255, 213, 0);
  --border-color-6:rgb(255, 0, 0);
  --text-color-1:rgb(0, 0, 0);
  --text-color-2:rgb(255, 255, 255);
  --text-color-3:rgb(0, 255, 128);
  --text-color-4:rgb(255, 128, 0);
  --text-color-5:rgb(255, 0, 128);
}
```
所有的顏色都使用 `var(--color-1)` 這樣的方式表示，而不是直接使用顏色值。
例如：
```
body {
  background-color: var(--color-1);
}
```
這樣做的好處是，當需要修改顏色時，只需要修改變數的值，而不需要修改每一個使用到這個顏色的地方。

生成 JavaScript 語法時，不可以使用內嵌 CSS 語法，例如：
```
document.body.style.backgroundColor = 'var(--color-1)';
```
這樣做會導致日後管理 CSS 樣式變很困難，因為所有的樣式都被寫在 JavaScript 裡面，而不是寫在 CSS 裡面。
所以，在生成 JavaScript 語法時，不可以使用內嵌 CSS 語法，而應該使用外部的 CSS 檔案。

生成的 UI 介面，顏色要依照我喜歡的顏色優先使用，避免使用純白色或純黑色，因為這樣會導致 UI 介面看起來很無聊。

index 檔案名稱為 index.php

所有生成的 CSS 跟 JavaScript 分別放在 CSS 跟 JS 資料夾裡面。
所有生成的 PHP 檔案都放在 PHP 資料夾裡面。
所有生成的 MySQL 語法都放在 MySQL 資料夾裡面。
每次添加新功能的時候，都要檢查 PHP 跟 MySQL 對應的語法是否正確。

整個方案都使用 html 跟 css 跟 js 跟 php 跟 mysql 來實現。

如果有測試檔跟 md 檔，要在每次對話結束以後刪除。

每次都要設計英文跟簡體中文的語言切換功能。

每次都要設計一個登入頁面，用戶可以在登入頁面輸入帳號跟密碼，然後登入到系統裡面。

每次都要設計一個註冊頁面，用戶可以在註冊頁面輸入帳號跟密碼，然後註冊一個新的帳號。

每次都要設計一個忘記密碼頁面，用戶可以在忘記密碼頁面輸入帳號，然後收到一個重置密碼的郵件。

每次都要設計一個用戶設定頁面，用戶可以在用戶設定頁面修改自己的個人資訊。