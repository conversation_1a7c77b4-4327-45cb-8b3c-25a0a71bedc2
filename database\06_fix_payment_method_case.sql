-- KMS PC Receipt Maker - Fix Payment Method Case
-- 修復付款方式的大小寫問題

USE kms_receipt_maker;

-- 更新現有的付款方式數據，將小寫轉換為正確的大小寫
UPDATE receipts 
SET payment_method = CASE 
    WHEN LOWER(payment_method) = 'cash' THEN 'Cash'
    WHEN LOWER(payment_method) = 'venmo' THEN 'Venmo'
    WHEN LOWER(payment_method) = 'zelle' THEN 'Zelle'
    WHEN LOWER(payment_method) = 'square' THEN 'Square'
    WHEN LOWER(payment_method) = 'stripe' THEN 'Stripe'
    ELSE payment_method
END
WHERE payment_method IS NOT NULL AND payment_method != '';

-- 檢查更新結果
SELECT id, receipt_number, payment_method, customer_name 
FROM receipts 
ORDER BY id DESC 
LIMIT 10;
