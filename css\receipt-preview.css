/**
 * Receipt Preview Styles
 * Extracted from inline CSS in main.js
 */

/* Main Receipt Container with Elegant Certificate Border */
.receipt-container {
    position: relative;
    max-width: 8.5in;
    margin: 0 auto;
    padding: 2rem 2.5rem;
    font-family: 'Courier New', monospace;
    font-size: 12pt;
    line-height: 1.4;
    background: white;
    border-radius: 60px; /* extra round corners */
    box-shadow:
        0 0 0 3px #D4AF37,
        0 0 0 6px white,
        0 0 0 9px #B8860B,
        0 0 0 12px white,
        0 0 0 15px #DAA520,
        0 8px 30px rgba(0, 0, 0, 0.2),
        inset 0 0 0 2px #F5DEB3;
    /* Elegant decorative pattern */
    background-image:
        radial-gradient(circle at 20px 20px, #F5DEB3 2px, transparent 2px),
        radial-gradient(circle at 60px 60px, #F5DEB3 1px, transparent 1px);
    background-size: 80px 80px, 40px 40px;
    background-position: 0 0, 20px 20px;
}

.receipt-container::before,
.receipt-container::after {
    content: '';
    position: absolute;
    z-index: -1;
}

.receipt-container::before {
    top: 20px;
    left: 20px;
    right: 20px;
    bottom: 20px;
    border: 1px solid #D4AF37;
    border-radius: 48px; /* match outer rounding feel */
    background: linear-gradient(45deg,
        transparent 0%,
        rgba(212, 175, 55, 0.1) 25%,
        transparent 50%,
        rgba(212, 175, 55, 0.1) 75%,
        transparent 100%);
}

.receipt-container::after {
    top: 30px;
    left: 30px;
    right: 30px;
    bottom: 30px;
    display: none !important;
}

.receipt-inner {
    position: relative;
    z-index: 1;
}

/* Logo Section */
.receipt-logo {
    text-align: center;
    margin-bottom: 1rem;
}

.receipt-logo img {
    max-height: 100px;
    max-width: 200px;
    object-fit: contain;
}

/* Receipt Header */
.preview-receipt-header {
    text-align: center;
    border-bottom: 2px solid #333;
    padding-bottom: 0.5rem;
    margin-bottom: 0.75rem;
}

.receipt-title {
    font-size: 1.8rem;
    font-weight: bold;
    color: #333;
    margin-bottom: 0.5rem;
}

.receipt-company-info {
    font-size: 0.9rem;
    color: #666;
    line-height: 1.3;
}

/* Receipt Info Section */
.preview-receipt-info {
    display: flex;
    justify-content: center;
    margin-bottom: 0.75rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #ddd;
}

.receipt-number {
    font-size: 1.1rem;
    font-weight: bold;
    color: #0d6efd;
}

/* Customer Information */
.preview-customer-info {
    margin-bottom: 0.75rem;
    padding: 0.5rem;
    background-color: #8efdeb;
    border-radius: 12px;
}

.preview-customer-info h6 {
    margin-bottom: 0.75rem;
    padding-bottom: 0.25rem;
    color: #333;
    font-weight: bold;
    border-bottom: 1px solid #ddd;
}

.customer-field {
    display: flex;
    align-items: center;
    margin-bottom: 0.35rem;
}

.customer-field-label {
    width: 90px;
    font-weight: bold;
    color: #555;
}

.customer-field-value {
    display: inline-block;
    min-width: 320px;
    height: 20px;
    line-height: 20px;
    color: #333;
    border-bottom: 1px solid #333;
}

.customer-field-value.handwrite-space {
    min-height: 25px;
    border-bottom: 2px solid #333;
    background: linear-gradient(to right, transparent 0%, transparent 98%, #ddd 98%, #ddd 100%);
}

/* Items Table */
.receipt-items {
    margin-bottom: 0.75rem;
}

.items-header {
    margin-bottom: 0.5rem;
    padding: 0.25rem 0;
    border-bottom: 1px solid #ddd;
}

.items-header h6 {
    margin: 0;
    color: #333;
    font-weight: bold;
}

.item-number {
    font-weight: bold;
    color: #0d6efd;
    background-color: #f8f9fa;
}

.receipt-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 1rem;
}

.receipt-table thead tr {
    background-color: #81c0ff;
}

.receipt-table th {
    padding: 4px 2px;
    color: #ffffff;
}

.receipt-table tbody tr {
    border-bottom: 1px solid #ddd;
}

.receipt-table td {
    padding: 4px 2px;
}

.receipt-table .text-left {
    text-align: left;
}

.receipt-table .text-center {
    text-align: center;
}

.receipt-table .text-right {
    text-align: right;
}

.item-name {
    font-weight: bold;
}

.item-description {
    color: #008e52;
    font-size: 16px;
}

.item-category {
    color: #0dcaf0;
    font-size: 16px;
}

/* Totals Section */
.receipt-totals {
    margin-top: 3px;
    padding-top: 3px;
    border-top: 2px solid #333;
}

.totals-table {
    width: 100%;
    max-width: 300px;
    margin-left: auto;
}

.totals-table td {
    padding: 0.5rem 1rem;
    border: none;
    font-family: 'Courier New', monospace;
    font-weight: bold;
}

.totals-table td.label {
    text-align: right;
    color: #555;
}

.totals-table td.amount {
    text-align: right;
}

.totals-table tr.total-row {
    font-size: 1.1rem;
    border-top: 1px solid #333;
}

.totals-table tr.total-row td {
    color: #333;
}

/* Payment Method Section */
.payment-method {
    margin-top: 0.75rem;
    padding: 0.75rem;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
}

.payment-method-title {
    margin-bottom: 0.75rem;
    font-size: 1.1rem;
    font-weight: bold;
    color: #333;
}

.payment-options {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    gap: 15px;
}

.payment-option-button {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    min-width: 80px;
    padding: 12px 8px;
    background: white;
    border: 2px solid #D4AF37;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.payment-option-button:hover {
    border-color: #B8860B;
    box-shadow: 0 2px 8px rgba(212, 175, 55, 0.3);
}

.payment-checkbox {
    position: relative;
    width: 20px;
    height: 20px;
    background: white;
    border: 2px solid #333;
    border-radius: 4px;
}

.payment-checkbox::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 6px;
    width: 6px;
    height: 10px;
    border: solid transparent;
    border-width: 0 2px 2px 0;
    opacity: 0;
    transform: rotate(45deg);
}

.payment-label {
    font-size: 0.9rem;
    font-weight: 600;
    color: #333;
    text-align: center;
}

/* Notes Section */
.receipt-notes {
    margin-top: 0.75rem;
    padding: 0.5rem;
    background-color: #fff3cd;
    border-left: 4px solid #ffc107;
    border-radius: 4px;
}

.receipt-notes h6 {
    margin-bottom: 0.5rem;
    color: #856404;
}

.receipt-notes p {
    margin: 0;
    color: #856404;
}

/* Signature Section */
.signature-section {
    margin-top: 1.5rem;
    padding-top: 1rem;
    font-size: 1rem;
    text-align: left;
    color: #333;
    border-top: 2px solid #333;
}

.signature-labels-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    padding: 0 1rem;
}

.signature-label-item {
    flex: 1;
    text-align: center;
}

.signature-label {
    font-size: 0.95rem;
    font-weight: bold;
    color: #333;
}

.signature-lines-area {
    margin-top: 0.5rem;
    padding: 0 1rem;
}

.signature-line-space {
    position: relative;
    width: 100%;
    height: 60px;
    border-bottom: 2px solid #333;
    background: linear-gradient(
        to right,
        transparent 0%,
        transparent 32%,
        #ddd 32%,
        #ddd 33%,
        transparent 33%,
        transparent 65%,
        #ddd 65%,
        #ddd 66%,
        transparent 66%,
        transparent 100%
    );
}

.signature-line-space::before,
.signature-line-space::after {
    content: '';
    position: absolute;
    top: 0;
    width: 1px;
    height: 100%;
    background: #ddd;
}

.signature-line-space::before {
    left: 33%;
}

.signature-line-space::after {
    right: 33%;
}

/* Enhanced Print Styles - Perfect Borderless with Beautiful Borders */
@media print {
    @page {
        size: letter;
        margin: 0 !important; /* True borderless printing */
    }

    .receipt-container {
        /* Perfect full-bleed print: fill entire Letter page */
        width: 8.5in !important;
        height: 11in !important;
        max-width: 8.5in !important;
        margin: 0 !important;
        padding: 0.3in !important; /* Optimal content padding */
        border: 4px solid #D4AF37 !important; /* Prominent gold border */
        border-radius: 60px !important; /* Beautiful rounded corners */
        box-shadow: none !important;
        box-sizing: border-box !important;
        overflow: hidden !important;
        page-break-inside: avoid;
        page-break-after: always;
        background: 
            radial-gradient(circle at 20px 20px, #F5DEB3 2px, transparent 2px),
            radial-gradient(circle at 60px 60px, #F5DEB3 1px, transparent 1px),
            white !important;
        background-size: 80px 80px, 40px 40px, auto !important;
        background-position: 0 0, 20px 20px, 0 0 !important;
        position: relative !important;
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
    }

    /* Remove page break from last container */
    .receipt-container:last-child {
        page-break-after: auto !important;
    }

    .receipt-container::before {
        /* Enhanced decorative border rings for perfect print */
        content: '' !important;
        position: absolute !important;
        top: 0.08in !important; /* Minimal inset for perfect borderless look */
        left: 0.08in !important;
        right: 0.08in !important;
        bottom: 0.08in !important;
        z-index: 1 !important;
        border: 3px solid #D4AF37 !important; /* Gold primary ring */
        border-radius: 54px !important; /* Perfectly aligned inner rounding */
        box-shadow:
            0 0 0 4px white,
            0 0 0 7px #B8860B,
            0 0 0 10px white,
            0 0 0 13px #DAA520,
            inset 0 0 0 3px #F5DEB3 !important;
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
        background: linear-gradient(45deg,
            transparent 0%,
            rgba(212, 175, 55, 0.05) 25%,
            transparent 50%,
            rgba(212, 175, 55, 0.05) 75%,
            transparent 100%) !important;
    }

    /* Enhanced inner decorative ring */
    .receipt-container::after {
        content: '' !important;
        position: absolute !important;
        display: block !important;
        top: 0.15in !important;
        left: 0.15in !important;
        right: 0.15in !important;
        bottom: 0.15in !important;
        z-index: 1 !important;
        border: 2px solid #B8860B !important; /* Dark goldenrod inner accent */
        border-radius: 48px !important; /* Perfectly proportioned inner ring */
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
    }

    /* Content positioning above border decorations */
    .receipt-inner {
        position: relative !important;
        z-index: 2 !important;
    }

    /* Enhanced table printing with border preservation */
    .receipt-table {
        page-break-inside: auto !important;
        width: 100% !important;
        border-collapse: collapse !important;
        font-size: 11px !important;
        line-height: 1.2 !important;
    }

    .receipt-table thead {
        display: table-header-group !important;
    }

    .receipt-table tbody tr {
        page-break-inside: avoid !important;
        break-inside: avoid !important;
    }

    .receipt-table td,
    .receipt-table th {
        padding: 3px 4px !important;
    }

    .item-name {
        font-size: 11px !important;
        margin-bottom: 2px !important;
    }

    .item-description {
        font-size: 9px !important;
        color: #666 !important;
        margin-bottom: 0 !important;
    }

    /* Keep important sections together */
    .receipt-totals,
    .payment-method,
    .receipt-notes,
    .signature-section {
        page-break-inside: avoid !important;
    }

    /* Enhanced color preservation */
    .preview-customer-info {
        background-color: #8efdeb !important;
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
    }

    .receipt-table thead tr {
        background-color: #81c0ff !important;
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
    }

    .payment-method {
        background-color: #f8f9fa !important;
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
    }

    .payment-option-button {
        border: 2px solid #D4AF37 !important;
        background: white !important;
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
    }

    .signature-line-space {
        border-bottom: 2px solid #333 !important;
    }
}