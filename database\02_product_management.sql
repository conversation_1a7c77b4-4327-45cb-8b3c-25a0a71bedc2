-- KMS PC Receipt Maker - Product Management Tables
-- 產品管理相關表格

USE kms_receipt_maker;

-- 電腦零件預設表
CREATE TABLE IF NOT EXISTS pc_parts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    category VARCHAR(50) NOT NULL,
    description TEXT,
    -- default_price: 舊欄位，保留相容性用途。若有 special_price，前端以 special_price 為主。
    default_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    -- 新增價格欄位：原價與特價
    original_price DECIMAL(10,2) NULL,
    special_price DECIMAL(10,2) NULL,
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_category (category),
    INDEX idx_name (name),
    INDEX idx_is_active (is_active),
    INDEX idx_sort_order (sort_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
