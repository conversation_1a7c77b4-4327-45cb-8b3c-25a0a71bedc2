/**
 * PDF Generator Module
 * Handles PDF generation functionality for receipts
 */
class PDFGenerator {
    constructor() {
        this.isGenerating = false;
    }

    /**
     * Generate PDF Receipt - Using html2canvas for exact styling
     */
    async generatePDF(receiptData, receiptNumber) {
        if (this.isGenerating) {
            console.log('PDF generation already in progress');
            return;
        }

        this.isGenerating = true;

        try {
            // Show loading message
            if (typeof UIManager !== 'undefined' && UIManager.showMessage) {
                UIManager.showMessage('正在生成PDF，請稍候...', 'info');
            } else {
                console.log('正在生成PDF，請稍候...');
            }

            // Get the receipt preview element
            const receiptElement = document.getElementById('receiptPreview');
            if (!receiptElement || !receiptElement.querySelector('.receipt-container')) {
                throw new Error('Receipt preview not found. Please generate receipt first.');
            }

            // Import jsPDF and html2canvas if not already loaded
            if (typeof window.jsPDF === 'undefined') {
                await this.loadScript('https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js');
            }
            if (typeof html2canvas === 'undefined') {
                await this.loadScript('https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js');
            }

            const { jsPDF } = window.jsPDF;

            // Create a temporary container for PDF generation
            const tempContainer = document.createElement('div');
            tempContainer.style.position = 'absolute';
            tempContainer.style.left = '-9999px';
            tempContainer.style.top = '0';
            tempContainer.style.width = '794px'; // A4 width in pixels at 96 DPI
            tempContainer.style.backgroundColor = 'white';
            tempContainer.style.padding = '20px';
            tempContainer.style.fontFamily = 'Arial, sans-serif';
            document.body.appendChild(tempContainer);

            // Generate receipt HTML for PDF
            const receiptHtml = window.ReceiptGenerator ? 
                window.ReceiptGenerator.generateReceiptHtml(receiptData) : 
                receiptElement.innerHTML;
            tempContainer.innerHTML = receiptHtml;

            // Apply PDF-specific styles
            const style = document.createElement('style');
            style.textContent = `
                .receipt-container {
                    max-width: 754px !important;
                    margin: 0 !important;
                    padding: 20px !important;
                    background: white !important;
                    box-shadow: none !important;
                    border: none !important;
                }
                .receipt-header { margin-bottom: 20px !important; }
                .receipt-table { width: 100% !important; }
                .receipt-table th, .receipt-table td { 
                    padding: 8px !important; 
                    border: 1px solid #ddd !important;
                }
                .signature-section { 
                    margin-top: 30px !important; 
                    page-break-inside: avoid !important;
                }
            `;
            tempContainer.appendChild(style);

            // Generate canvas from the temporary container
            const canvas = await html2canvas(tempContainer, {
                scale: 2,
                useCORS: true,
                allowTaint: true,
                backgroundColor: '#ffffff',
                width: 794,
                height: tempContainer.scrollHeight
            });

            // Remove temporary container
            document.body.removeChild(tempContainer);

            // Create PDF
            const pdf = new jsPDF('p', 'mm', 'a4');
            const imgWidth = 210; // A4 width in mm
            const pageHeight = 297; // A4 height in mm
            const imgHeight = (canvas.height * imgWidth) / canvas.width;
            let heightLeft = imgHeight;
            let position = 0;

            // Add first page
            pdf.addImage(canvas.toDataURL('image/png'), 'PNG', 0, position, imgWidth, imgHeight);
            heightLeft -= pageHeight;

            // Add additional pages if needed
            while (heightLeft >= 0) {
                position = heightLeft - imgHeight;
                pdf.addPage();
                pdf.addImage(canvas.toDataURL('image/png'), 'PNG', 0, position, imgWidth, imgHeight);
                heightLeft -= pageHeight;
            }

            // Save the PDF
            const filename = `Receipt_${receiptNumber || 'KMS-UltraVIP-0000001'}_${new Date().toISOString().slice(0, 10)}.pdf`;
            pdf.save(filename);

            if (typeof UIManager !== 'undefined' && UIManager.showMessage) {
                UIManager.showMessage('PDF已成功生成並下載', 'success');
            } else {
                console.log('PDF已成功生成並下載');
            }

        } catch (error) {
            console.error('PDF generation error:', error);
            if (typeof UIManager !== 'undefined' && UIManager.showMessage) {
                UIManager.showMessage('PDF生成失敗: ' + error.message, 'error');
            } else {
                alert('PDF生成失敗: ' + error.message);
            }
        } finally {
            this.isGenerating = false;
        }
    }

    /**
     * Load external script dynamically
     */
    loadScript(src) {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = src;
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }
}

// Create global instance
window.PDFGenerator = new PDFGenerator();
