<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="KMS Receipt Maker is a tool to easily create, manage, and print customer receipts.">
    <title data-lang="app_title">KMS Receipt Maker</title>
    
    <!-- Bootstrap CSS -->
    <link href="css/bootstrap.css" rel="stylesheet">
    <!-- Font Awesome removed - using Unicode symbols instead -->
    <!-- Custom CSS -->
    <link href="css/style.css" rel="stylesheet">
    <link href="css/receipt.css" rel="stylesheet">
    <link href="css/receipt-preview.css" rel="stylesheet">
    <link href="css/messages-modals.css" rel="stylesheet">
    <link href="css/custom-modals.css" rel="stylesheet">
    <link href="css/dynamic-styles.css" rel="stylesheet">
</head>
<body>

    <!-- =================================================================
    RIGHT-SIDE QUICK NAVIGATION
    ================================================================== -->
    <nav class="quick-nav" aria-label="Section shortcuts">
        <a href="#anchorCreate" title="Create New Receipt" aria-label="Create New Receipt"><span class="icon-symbol">📄</span></a>
        <a href="#anchorAddItem" title="Add Item" aria-label="Add Item"><span class="icon-symbol">➕</span></a>
        <a href="#anchorReceiptItems" title="Receipt Items" aria-label="Receipt Items"><span class="icon-symbol">📋</span></a>
        <a href="#anchorReceiptPreview" title="Receipt Preview" aria-label="Receipt Preview"><span class="icon-symbol">👁</span></a>
    </nav>

    <!-- =================================================================
    MODERN NAVIGATION BAR
    ================================================================== -->
    <nav class="kms-navbar">
        <div class="kms-nav-container">
            <div class="kms-brand">
                <span class="icon-symbol kms-brand-icon">🧾</span>
                <span class="kms-brand-text" data-lang="app_title">KMS Receipt Maker</span>
            </div>
            <div class="kms-nav-menu">
                <button class="kms-nav-btn active" onclick="showSection('create')" data-section="create">
                    <span class="icon-symbol">➕</span>
                    <span data-lang="nav_create">Create Receipt</span>
                </button>
                <button class="kms-nav-btn" onclick="showSection('history')" data-section="history">
                    <span class="icon-symbol">📜</span>
                    <span data-lang="nav_history">History</span>
                </button>
                <div class="kms-language-group">
                    <button class="kms-lang-btn" onclick="changeLanguage('en')" data-lang-code="en">
                        <span class="icon-symbol">🌐</span>
                        <span>English</span>
                    </button>
                    <button class="kms-lang-btn" onclick="changeLanguage('zh')" data-lang-code="zh">
                        <span class="icon-symbol">🌐</span>
                        <span>繁體中文</span>
                    </button>
                </div>
            </div>
            <button class="kms-mobile-toggle" onclick="toggleMobileMenu()">
                <span></span>
                <span></span>
                <span></span>
            </button>
        </div>
    </nav>

    <!-- =================================================================
    MAIN CONTENT AREA
    ================================================================== -->
    <main class="kms-container" role="main">
        <div class="kms-main-content">

            <!-- Create Receipt Section -->
            <section id="createSection" class="section active" aria-labelledby="create-heading">
                <div class="kms-content-wrapper">
                    <!-- Receipt Form Card -->
                    <div class="card mb-1" id="anchorCreate">
                        <header class="card-header">
                            <h5 class="card-title mb-0" id="create-heading">
                                <span class="icon-symbol me-2">➕</span>
                                <span data-lang="create_receipt">創建新收據</span>
                            </h5>
                        </header>
                        <div class="card-body">
                            <form id="receiptForm">
                                <div class="row">
                                    <!-- Receipt Info -->
                                    <div class="col-md-6 mb-1">
                                        <h3 class="text-primary border-bottom pb-2 mb-3" data-lang="receipt_info">收據信息</h3>
                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                <label for="receiptNumber" class="form-label" data-lang="receipt_number">收據編號</label>
                                                <input type="text" class="form-control" id="receiptNumber" placeholder="自動生成">
                                            </div>
                                            <div class="col-12 mb-3">
                                                <label for="logoUpload" class="form-label" data-lang="upload_logo">上傳 Logo</label>
                                                <input type="file" class="form-control" id="logoUpload" accept="image/*" onchange="handleLogoUpload(event)">
                                                <!-- Logo Preview Area -->
                                                <div id="logoPreview" class="logo-preview mt-3">
                                                    <div class="card">
                                                        <header class="card-header">
                                                            <h3 class="mb-0" data-lang="logo_preview">Logo 預覽</h3>
                                                        </header>
                                                        <div class="card-body">
                                                            <div class="row">
                                                                <div class="col-md-6">
                                                                    <img id="logoPreviewImage" src="" alt="Logo Preview" class="img-fluid rounded">
                                                                </div>
                                                                <div class="col-md-6">
                                                                    <h3 data-lang="logo_info">圖片資訊</h3>
                                                                    <ul class="list-unstyled">
                                                                        <li><strong data-lang="file_name">檔案名稱:</strong> <span id="logoFileName"></span></li>
                                                                        <li><strong data-lang="file_type">檔案類型:</strong> <span id="logoFileType"></span></li>
                                                                        <li><strong data-lang="file_size">檔案大小:</strong> <span id="logoFileSize"></span></li>
                                                                        <li><strong data-lang="image_dimensions">圖片尺寸:</strong> <span id="logoImageDimensions"></span></li>
                                                                    </ul>
                                                                    <button type="button" class="btn btn-danger btn-sm" onclick="removeLogo()">
                                                                        <span class="icon-symbol me-1">🗑</span>
                                                                        <span data-lang="remove_logo">移除 Logo</span>
                                                                    </button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- Customer Info -->
                                    <div class="col-md-6 mb-4">
                                        <div class="customer-info-section">
                                            <h3 class="text-primary border-bottom pb-2 mb-4" data-lang="customer_info">客戶信息</h3>
                                        <div class="row g-3">
                                            <div class="col-md-6">
                                                <label for="customerName" class="form-label fw-semibold" data-lang="customer_name">客戶姓名</label>
                                                <input type="text" class="form-control" id="customerName" placeholder="Name">
                                            </div>
                                            <div class="col-md-6">
                                                <label for="customerPhone" class="form-label fw-semibold" data-lang="customer_phone">聯絡電話</label>
                                                <input type="tel" class="form-control" id="customerPhone" placeholder="Cell Phone Number">
                                            </div>
                                            <div class="col-12">
                                                <label for="customerEmail" class="form-label fw-semibold" data-lang="customer_email">電子郵件</label>
                                                <input type="email" class="form-control" id="customerEmail" placeholder="E-mail">
                                            </div>
                                            <div class="col-12">
                                                <label for="customerAddress" class="form-label fw-semibold" data-lang="customer_address">地址</label>
                                                <textarea class="form-control customer-address-textarea" id="customerAddress" rows="2" placeholder="Address"></textarea>
                                            </div>
                                            <div class="col-12">
                                                <label for="notes" class="form-label fw-semibold" data-lang="notes">備註</label>
                                                <textarea class="form-control notes-textarea" id="notes" rows="2" placeholder="Notes"></textarea>
                                            </div>
                                        </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Add Item Card -->
                    <div class="card mb-1" id="anchorAddItem">
                        <header class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">
                                    <span class="icon-symbol me-2">➕</span>
                                    <span data-lang="add_item">Add Item</span>
                                </h5>
                                <button type="button" class="btn btn-select-preset btn-sm" onclick="showPresetModal()">
                                    <span class="icon-symbol me-1">📋</span>
                                    <span data-lang="select_preset">Select Preset</span>
                                </button>
                            </div>
                        </header>
                        <div class="card-body text-center">
                            <p class="text-muted mb-3" data-lang="add_item_description">Click the button below to add items to the receipt</p>
                            <button type="button" class="btn btn-primary btn-lg" onclick="showAddItemModal()">
                                <span class="icon-symbol me-2">➕</span>
                                <span data-lang="add_item">Add Item</span>
                            </button>
                        </div>
                    </div>

                    <!-- Items List & Totals Card -->
                    <div class="card mb-1" id="anchorReceiptItems">
                        <header class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">
                                    <span class="icon-symbol me-2">📋</span>
                                    <span data-lang="receipt_items">Receipt Items</span>
                                </h5>
                                <div class="d-flex gap-2">
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="UIManager.toggleCompactView()" title="Toggle Compact View">
                                            <span class="icon-symbol">⇅</span>
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="UIManager.toggleDragAndDrop()" title="Toggle Drag & Drop">
                                            <span class="icon-symbol">↔</span>
                                        </button>
                                    </div>
                                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="clearAllItems()">
                                        <span class="icon-symbol me-1">🗑</span>
                                        <span data-lang="clear_all">Clear All</span>
                                    </button>
                                </div>
                                <div class="d-flex gap-3">
                                    <div class="form-group mb-0">
                                        <label for="discountAmount" class="form-label me-2" data-lang="discount">Discount:</label>
                                        <input type="number" class="form-control form-control-sm d-inline-block discount-input" id="discountAmount" min="0" step="0.01" value="0">
                                    </div>
                                    <div class="form-group mb-0">
                                        <label for="taxRate" class="form-label me-2" data-lang="tax_rate">Tax Rate:</label>
                                        <input type="number" class="form-control form-control-sm d-inline-block tax-input" id="taxRate" min="0" max="100" step="0.1" value="0">
                                        <span class="text-muted">%</span>
                                    </div>
                                </div>
                            </div>
                        </header>
                        <div class="card-body">
                            <div id="receiptItemsList">
                                <div class="text-center text-muted py-4">
                                    <span class="icon-symbol large">🛒</span>
                                    <p data-lang="no_items">尚未添加任何項目</p>
                                </div>
                            </div>
                            <div class="mt-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="d-flex gap-2">
                                        <button type="button" class="btn btn-primary" onclick="generateReceipt()">
                                            <span class="icon-symbol me-1">📄</span>
                                            <span data-lang="generate_receipt">生成收據</span>
                                        </button>
                                        <button type="button" class="btn btn-outline-success" onclick="saveReceiptConfiguration()">
                                            <span class="icon-symbol me-1">💾</span>
                                            <span data-lang="save_configuration">保存配置</span>
                                        </button>
                                        <button type="button" class="btn btn-outline-info" onclick="showConfigurationModal()">
                                            <span class="icon-symbol me-1">⚙</span>
                                            <span data-lang="manage_configurations">管理配置</span>
                                        </button>
                                    </div>
                                    <div id="totalsDisplay" class="text-end">
                                        <!-- Totals will be displayed here -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Receipt Preview Card -->
                    <div class="card mt-3" id="anchorReceiptPreview">
                        <header class="card-header">
                            <h5 class="card-title mb-0">
                                <span class="icon-symbol me-2">👁</span>
                                <span data-lang="receipt_preview">收據預覽</span>
                            </h5>
                        </header>
                        <div class="card-body">
                            <div id="receiptPreview" class="receipt-preview">
                                <div class="text-center text-muted">
                                    <span class="icon-symbol extra-large">📄</span>
                                    <p data-lang="preview_placeholder">收據預覽將在這裡顯示</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Bottom Actions -->
                    <div id="bottomActions" class="text-center my-4">
                        <div class="btn-group" role="group" aria-label="Receipt actions">
                            <button type="button" class="btn btn-success" onclick="saveReceipt()">
                                <span class="icon-symbol me-1">💾</span>
                                <span data-lang="save_receipt">保存收據</span>
                            </button>
                            <button type="button" class="btn btn-info" onclick="printReceipt()">
                                <span class="icon-symbol me-1">🖨</span>
                                <span data-lang="print_receipt">打印收據</span>
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- History Section -->
            <section id="historySection" class="section" aria-labelledby="history-heading">
                <div class="kms-content-wrapper">
                    <div class="card">
                        <header class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0" id="history-heading">
                                    <span class="icon-symbol me-2">📜</span>
                                    <span data-lang="receipt_history">收據歷史</span>
                                </h5>
                                <div class="d-flex gap-2 align-items-center flex-wrap">
                                    <input type="text" class="form-control form-control-sm search-input-width" id="searchInput" data-lang="search_receipts" placeholder="搜索收據..." onkeypress="handleSearchKeyPress(event)">
                                    <select class="form-select form-select-sm payment-filter-width" id="paymentFilter" onchange="searchReceipts()">
                                        <option value="" data-lang="all_payment_methods">所有付款方式</option>
                                        <option value="Cash">Cash</option>
                                        <option value="Venmo">Venmo</option>
                                        <option value="Zelle">Zelle</option>
                                        <option value="Square">Square</option>
                                        <option value="Stripe">Stripe</option>
                                    </select>
                                    <input type="date" class="form-control form-control-sm date-filter-width" id="dateFromFilter" onchange="searchReceipts()">
                                    <span class="text-muted">至</span>
                                    <input type="date" class="form-control form-control-sm date-filter-width" id="dateToFilter" onchange="searchReceipts()">
                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="searchReceipts()"><span class="icon-symbol">🔍</span></button>
                                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearFilters()"><span class="icon-symbol">✖</span></button>
                                    <div class="vr"></div>
                                    <button type="button" class="btn btn-outline-info btn-sm" onclick="selectAllReceipts()" title="Select All"><span class="icon-symbol">☑</span></button>
                                    <button type="button" class="btn btn-outline-warning btn-sm" onclick="clearSelection()" title="Clear Selection"><span class="icon-symbol">☐</span></button>
                                </div>
                            </div>
                        </header>
                        <div class="card-body">
                            <div id="receiptHistory">
                                <!-- History will be loaded here -->
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- =================================================================
    MODALS
    ================================================================== -->

    <!-- Preset Selection Modal -->
    <div id="presetModal" class="kms-modal" aria-hidden="true" role="dialog" aria-modal="true" aria-labelledby="presetModalTitle">
        <div class="kms-modal-dialog" role="document">
            <header class="kms-modal-header">
                <h5 class="kms-modal-title" id="presetModalTitle" data-lang="select_preset">選擇預設項目</h5>
                <div class="toolbar-row">
                    <button type="button" class="btn-action btn-action-select" onclick="showAddPresetForm()" title="新增預設">
                        <span class="icon-symbol me-1">➕</span>
                        <span data-lang="add_preset">新增預設</span>
                    </button>
                    <button type="button" class="kms-modal-close" onclick="hidePresetModal()" aria-label="Close">×</button>
                </div>
            </header>
            <div class="kms-modal-body">
                <!-- Add Preset Form -->
                <div id="addPresetForm" class="card mb-3 add-preset-form-hidden">
                    <header class="card-header">
                        <h3 class="mb-0" data-lang="add_preset">新增預設項目</h3>
                    </header>
                    <div class="card-body">
                        <div class="form-grid">
                            <div class="col-6">
                                <label class="kms-label" for="presetItemName" data-lang="item_name">項目名稱</label>
                                <input type="text" class="kms-input" id="presetItemName" required>
                            </div>
                            <div class="col-6">
                                <label class="kms-label" for="presetItemCategory" data-lang="item_category">分類</label>
                                <select class="kms-select" id="presetItemCategory">
                                    <option value="PC Case">PC Case</option>
                                    <option value="CPU">CPU</option>
                                    <option value="CPU Cooler">CPU Cooler</option>
                                    <option value="GPU">GPU</option>
                                    <option value="RAM">RAM</option>
                                    <option value="SSD">SSD</option>
                                    <option value="Motherboard">Motherboard</option>
                                    <option value="PSU">PSU</option>
                                    <option value="MB RGB">MB RGB</option>
                                    <option value="GPU RGB">GPU RGB</option>
                                    <option value="Fan RGB">Fan RGB</option>
                                    <option value="Other">Other</option>
                                    <option value="Services">Services</option>
                                </select>
                            </div>
                            <div class="col-4">
                                <label class="kms-label" for="presetItemOriginalPrice" data-lang="original_price">原價</label>
                                <input type="number" class="kms-input" id="presetItemOriginalPrice" min="0" step="0.01">
                            </div>
                            <div class="col-4">
                                <label class="kms-label" for="presetItemSpecialPrice" data-lang="special_price">特價</label>
                                <input type="number" class="kms-input" id="presetItemSpecialPrice" min="0" step="0.01">
                            </div>
                            <div class="col-4">
                                <label class="kms-label" for="presetItemDiscountPercent" data-lang="discount_percentage">折扣百分比</label>
                                <div class="input-inline">
                                    <input type="number" class="kms-input" id="presetItemDiscountPercent" readonly>
                                    <span class="input-suffix">%</span>
                                </div>
                            </div>
                            <div class="col-12">
                                <label class="kms-label" for="presetItemDescription" data-lang="item_description">描述</label>
                                <input type="text" class="kms-input" id="presetItemDescription">
                            </div>
                            <div class="col-12">
                                <div class="toolbar-row">
                                    <button type="button" class="btn-action btn-action-select" onclick="savePresetItem()">
                                        <span class="icon-symbol me-1">💾</span>
                                        <span data-lang="save">保存</span>
                                    </button>
                                    <button type="button" class="btn-action btn-action-toggle" onclick="cancelAddPreset()">
                                        <span class="icon-symbol me-1">✖</span>
                                        <span data-lang="cancel">取消</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="toolbar-row">
                    <div class="grow">
                        <input type="text" class="kms-input" id="presetSearch" data-lang="search_items" placeholder="搜索項目..." onkeyup="filterPresets()">
                    </div>
                    <div>
                        <select class="kms-select" id="presetCategoryFilter" onchange="filterPresets()">
                            <option value="" data-lang="all_categories">所有分類</option>
                            <option value="PC Case">PC Case</option>
                            <option value="CPU">CPU</option>
                            <option value="CPU Cooler">CPU Cooler</option>
                            <option value="GPU">GPU</option>
                            <option value="RAM">RAM</option>
                            <option value="SSD">SSD</option>
                            <option value="Motherboard">Motherboard</option>
                            <option value="PSU">PSU</option>
                            <option value="MB RGB">MB RGB</option>
                            <option value="GPU RGB">GPU RGB</option>
                            <option value="Fan RGB">Fan RGB</option>
                            <option value="Other">Other</option>
                            <option value="Services">Services</option>
                        </select>
                    </div>
                </div>
                <div id="presetList">
                    <!-- Preset items will be loaded here -->
                </div>
            </div>
            <footer class="kms-modal-footer">
                <button type="button" class="btn-action btn-action-toggle" onclick="hidePresetModal()" data-lang="close">關閉</button>
            </footer>
        </div>
    </div>

    <!-- Receipt Details Modal -->
    <div class="modal fade" id="receiptModal" tabindex="-1" aria-labelledby="receiptModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <header class="modal-header">
                    <h5 class="modal-title" id="receiptModalLabel" data-lang="receipt_details">收據詳情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </header>
                <div class="modal-body">
                    <div id="receiptModalContent">
                        <!-- Receipt content will be loaded here -->
                    </div>
                </div>
                <footer class="modal-footer">
                    <button type="button" class="btn btn-info" onclick="printModalReceipt()">
                        <span class="icon-symbol me-1">🖨</span>
                        <span data-lang="print_receipt">打印收據</span>
                    </button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" data-lang="close">關閉</button>
                </footer>
            </div>
        </div>
    </div>

    <!-- Configuration Management Modal -->
    <div class="modal fade" id="configurationModal" tabindex="-1" aria-labelledby="configModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <header class="modal-header">
                    <h5 class="modal-title" id="configModalLabel" data-lang="manage_configurations">管理配置</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </header>
                <div class="modal-body">
                    <div id="configurationList" class="configuration-list-container">
                        <!-- Configuration items will be loaded here -->
                    </div>
                </div>
                <footer class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" data-lang="close">關閉</button>
                </footer>
            </div>
        </div>
    </div>

    <!-- Add Item Modal -->
    <div class="modal fade" id="addItemModal" tabindex="-1" aria-labelledby="addItemModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <header class="modal-header">
                    <h5 class="modal-title" id="addItemModalLabel">
                        <span class="icon-symbol me-2">➕</span>
                        <span data-lang="add_item">Add Item</span>
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </header>
                <div class="modal-body">
                    <form id="addItemModalForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="modalItemName" class="form-label" data-lang="item_name">Item Name *</label>
                                <input type="text" class="form-control" id="modalItemName" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="modalItemCategory" class="form-label" data-lang="item_category">Category</label>
                                <select class="form-select" id="modalItemCategory">
                                    <option value="PC Case">PC Case</option>
                                    <option value="CPU">CPU</option>
                                    <option value="CPU Cooler">CPU Cooler</option>
                                    <option value="GPU">GPU</option>
                                    <option value="RAM">RAM</option>
                                    <option value="SSD">SSD</option>
                                    <option value="Motherboard">Motherboard</option>
                                    <option value="PSU">PSU</option>
                                    <option value="MB RGB">MB RGB</option>
                                    <option value="GPU RGB">GPU RGB</option>
                                    <option value="Fan RGB">Fan RGB</option>
                                    <option value="Other">Other</option>
                                    <option value="Services">Services</option>
                                </select>
                            </div>
                            <div class="col-12 mb-3">
                                <label for="modalItemDescription" class="form-label" data-lang="item_description">Description</label>
                                <textarea class="form-control" id="modalItemDescription" rows="2"></textarea>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="modalItemQuantity" class="form-label" data-lang="quantity">Quantity *</label>
                                <input type="number" class="form-control" id="modalItemQuantity" min="1" value="1" required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="modalItemOriginalPrice" class="form-label" data-lang="original_price">Original Price</label>
                                <input type="number" class="form-control" id="modalItemOriginalPrice" min="0" step="0.01">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="modalItemSpecialPrice" class="form-label" data-lang="special_price">Special Price *</label>
                                <input type="number" class="form-control" id="modalItemSpecialPrice" min="0" step="0.01" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="modalItemDiscountPercent" class="form-label" data-lang="discount_percentage">Discount Percentage</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="modalItemDiscountPercent" readonly>
                                    <span class="input-group-text">%</span>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="modalItemTotal" class="form-label" data-lang="total_price">Total Price</label>
                                <input type="number" class="form-control" id="modalItemTotal" readonly>
                            </div>
                            <div class="col-12 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="modalItemHidePrice">
                                    <label class="form-check-label" for="modalItemHidePrice" data-lang="hide_price">
                                        Hide Price (Show N/A on receipt)
                                    </label>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <footer class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" data-lang="cancel">Cancel</button>
                    <button type="button" class="btn btn-success" onclick="confirmAddModalItem()">
                        <span class="icon-symbol me-1">✓</span>
                        <span data-lang="confirm_add">Confirm Add</span>
                    </button>
                </footer>
            </div>
        </div>
    </div>

    <!-- =================================================================
    JAVASCRIPTS
    ================================================================== -->
    <!-- Libraries -->
    <script src="js/bootstrap-replacement.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    
    <!-- Custom Scripts (in loading order) -->
    <script src="js/custom-modals.js"></script>
    <script src="js/language.js"></script>
    <script src="js/utils.js"></script>

    <!-- Core modules -->
    <script src="js/core/app-initializer.js"></script>

    <!-- Feature modules -->
    <script src="js/modules/item-manager.js"></script>
    <script src="js/modules/receipt-generator.js"></script>
    <script src="js/modules/ui-manager.js"></script>

    <!-- Main coordinator -->
    <script src="js/main.js"></script>

    <!-- Additional functionalities -->
    <script src="js/receipt-delete.js"></script>
    <script src="js/preset-manager.js"></script>
    <script src="js/config-manager.js"></script>
    <script src="js/receipt.js"></script>

</body>
</html>