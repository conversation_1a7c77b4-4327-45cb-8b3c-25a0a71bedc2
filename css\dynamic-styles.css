/* KMS Receipt Maker - Dynamic Styles (Previously JS Generated) */
/* Only non-duplicate styles that are not already in other CSS files */

/* =================================================================
   PRESET MANAGER STYLES
   ================================================================= */

/* Preset Buttons Row - New style for horizontal button layout */
.preset-buttons-row {
    display: flex;
    gap: 4px;
    align-items: center;
}

/* =================================================================
   ITEM MANAGER STYLES
   ================================================================= */

/* Receipt Item Spacing Adjustments */
.receipt-item {
    padding: 3px !important;
    margin-bottom: 3px !important;
}

.receipt-item .col-md-4,
.receipt-item .col-md-2,
.receipt-item .col-md-1 {
    padding: 3px !important;
}

/* Ensure item text sits to the right of the drag handle */
.receipt-item.draggable {
    padding-left: 56px !important;
}

/* Ensure drag handle has predictable width so text never overlaps */
.receipt-item.draggable .drag-handle {
    width: 24px;
    text-align: center;
}

.item-info h6 {
    margin-bottom: 5px !important;
}

.item-description {
    margin-top: 5px !important;
    margin-bottom: 0 !important;
}

/* New Receipt Items Display Styles */
.item-price-display,
.item-discount-display,
.item-special-price {
    font-weight: 600;
    color: #333;
    padding: 8px 0;
    font-size: 14px;
}

.item-discount-display {
    color: #28a745;
}

.item-special-price {
    color: #dc3545;
}

/* Item Total Price - Additional styles only (base styles in receipt.css) */
.item-total-price {
    padding: 8px 0;
}

/* =================================================================
   RECEIPT TABLE PRINT STYLES
   ================================================================= */

/* Compact table row for print */
.receipt-table-row-compact {
    line-height: 1.1;
}

/* Compact table cells */
.receipt-table-cell-compact {
    padding: 3px;
    font-size: 9px;
}

.receipt-table-cell-number {
    font-size: 10px;
}

.receipt-table-cell-price {
    font-size: 9px;
}

/* Table headers with specific widths */
.receipt-table-header-number {
    width: 25px;
}

.receipt-table-header-number-wide {
    width: 40px;
}

.receipt-table-header-price {
    width: 60px;
}

.receipt-table-header-discount {
    width: 45px;
}

.receipt-table-header-qty {
    width: 35px;
}

.receipt-table-header-total {
    width: 70px;
}

/* Item name and description in table */
.receipt-table-item-name {
    font-size: 10px;
    margin-bottom: 1px;
}

.receipt-table-item-desc {
    font-size: 8px;
    color: #666;
    margin-bottom: 0;
}

/* =================================================================
   PRINT SPECIFIC STYLES
   ================================================================= */

/* Print Page Continuation Styles */
.print-page-continuation {
    text-align: center;
    margin: 20px 0;
    font-weight: bold;
    color: #666;
}

/* Print Totals Margin */
.print-totals-margin {
    margin-top: 40px;
}

.print-totals-margin-small {
    margin-top: 20px;
}

/* Print Payment Method Margin */
.print-payment-method-margin {
    margin-top: 20px;
}

/* Print Notes Margin */
.print-notes-margin {
    margin-top: 20px;
}

/* Print Signature Margin */
.print-signature-margin {
    margin-top: 30px;
}

/* Enhanced Print Page Break Control */
@media print {

    .receipt-totals,
    .payment-method,
    .receipt-notes,
    .signature-section {
        page-break-inside: avoid !important;
        break-inside: avoid !important;
    }

    .signature-section {
        min-height: 180px !important;
        margin-bottom: 30px !important;
        page-break-before: auto !important;
    }

    .receipt-container {
        min-height: 85vh !important;
    }

    /* Force signature section to stay together */
    .signature-labels-row,
    .signature-lines-area {
        page-break-inside: avoid !important;
        break-inside: avoid !important;
    }
}

/* =================================================================
   FORM STYLES (Previously Inline)
   ================================================================= */

/* Customer Address and Notes Textareas */
.customer-address-textarea {
    width: 577px;
    height: 100px;
}

.notes-textarea {
    width: 577px;
    height: 90px;
}

/* Search and Filter Inputs */
.search-input-width {
    width: 200px;
}

.payment-filter-width {
    width: 120px;
}

.date-filter-width {
    width: 150px;
}

/* Configuration List */
.configuration-list-container {
    max-height: 400px;
    overflow-y: auto;
}

/* Add Preset Form */
.add-preset-form-hidden {
    display: none !important;
}

/* =================================================================
   LOGO DRAG AND DROP STYLES
   ================================================================= */

/* Draggable Logo - Additional styles only (base styles in receipt.css) */
.draggable-logo {
    position: absolute;
    z-index: 10;
}

/* Resize Handle - Additional styles only (base styles in receipt.css) */
.resize-handle {
    z-index: 11;
}

/* =================================================================
   RESPONSIVE STYLES
   ================================================================= */

@media (max-width: 768px) {

    .customer-address-textarea,
    .notes-textarea {
        width: 100%;
        max-width: 100%;
    }

    .search-input-width,
    .payment-filter-width,
    .date-filter-width {
        width: 100%;
        margin-bottom: 8px;
    }

    .preset-buttons-row {
        flex-direction: column;
        gap: 2px;
    }
}

@media (max-width: 576px) {
    .preset-buttons-row {
        flex-direction: column;
        gap: 2px;
    }
}