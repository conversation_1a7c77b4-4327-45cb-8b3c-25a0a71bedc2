/**
 * ===================================================================
 * Messages and Modals Styles
 * Toast messages, alerts, and modal dialogs
 * ===================================================================
 */

/* =================================== */
/* ===== Core & Variables         ===== */
/* =================================== */

:root {
    /* Brand Colors */
    --kms-primary: #00a5ff;
    --kms-primary-light: #1fb5db;
    --kms-secondary: #00a16d;
    --kms-link: #0d6efd;
    --kms-link-bg: rgba(13, 110, 253, 0.15);

    /* Text Colors */
    --kms-text-light: #ffffff;
    --kms-text-dark: #212529;
    --kms-text-muted: #6c757d;
    --kms-text-label: #495057;

    /* Background & Border Colors */
    --kms-bg-light: #f8f9fa;
    --kms-bg-widget: #f1f3f5;
    --kms-border-color: #dee2e6;
    --kms-border-light: #e9ecef;
    --kms-border-focus: #0d6efd;

    /* Semantic Colors */
    --kms-success: #0f5132;
    --kms-success-bg: #d1e7dd;
    --kms-success-border: #badbcc;
    --kms-info: #055160;
    --kms-info-bg: #d1ecf1;
    --kms-info-border: #bee5eb;
    --kms-warning: #664d03;
    --kms-warning-bg: #fff3cd;
    --kms-warning-border: #ffecb5;
    --kms-danger: #842029;
    --kms-danger-bg: #f8d7da;
    --kms-danger-border: #f5c2c7;

    /* Golden Button Colors */
    --kms-gold-start: #FFD700;
    --kms-gold-mid: #FFA500;
    --kms-gold-end: #FF8C00;
    --kms-gold-border: #DAA520;
    --kms-gold-text: #8B4513;
    --kms-gold-hover-start: #FFED4E;
    --kms-gold-hover-mid: #FFB347;
    --kms-gold-hover-end: #FF7F50;
    --kms-gold-hover-border: #B8860B;
    --kms-gold-hover-text: #654321;
}

/* Body helper to prevent background scrolling when modal is open */
body.kms-no-scroll {
    overflow: hidden;
}


/* =================================== */
/* ===== Toast Messages & Alerts  ===== */
/* =================================== */

/* Toast Messages */
.toast-message {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    min-width: 300px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-radius: 8px;
    animation: slideInRight 0.3s ease-out;
}

.toast-message.removing {
    animation: slideOutRight 0.3s ease-in;
}

/* Alert Variants */
.alert-success {
    background-color: var(--kms-success-bg);
    border-color: var(--kms-success-border);
    color: var(--kms-success);
}

.alert-info {
    background-color: var(--kms-info-bg);
    border-color: var(--kms-info-border);
    color: var(--kms-info);
}

.alert-warning {
    background-color: var(--kms-warning-bg);
    border-color: var(--kms-warning-border);
    color: var(--kms-warning);
}

.alert-danger {
    background-color: var(--kms-danger-bg);
    border-color: var(--kms-danger-border);
    color: var(--kms-danger);
}


/* =================================== */
/* ===== Pure CSS Modal (Base)    ===== */
/* =================================== */
#presetModal.kms-modal {
    position: fixed;
    inset: 0;
    display: none;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.45);
    z-index: 1050;
}

#presetModal.kms-modal.is-open {
    display: flex;
}

#presetModal .kms-modal-dialog {
    width: min(1200px, 95vw);
    max-height: 90vh;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.35);
    background: var(--kms-text-light);
    display: flex;
    flex-direction: column;
}

/* Merged .modal-header styles into here */
#presetModal .kms-modal-header {
    background: var(--kms-primary);
    color: var(--kms-text-light);
    padding: 6px 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: none;
}

/* Merged .modal-title styles into here */
#presetModal .kms-modal-title {
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0;
}

#presetModal .kms-modal-close {
    appearance: none;
    border: 0;
    background: transparent;
    color: var(--kms-text-light);
    font-size: 1.25rem;
    line-height: 1;
    padding: 0.25rem;
    border-radius: 6px;
    cursor: pointer;
    filter: invert(1);
}

#presetModal .kms-modal-close:hover {
    background: rgba(255, 255, 255, 0.15);
}

/* Merged .modal-body styles into here */
#presetModal .kms-modal-body {
    padding: 1rem;
    overflow: auto;
    background-color: var(--kms-primary-light);
}

/* Merged .modal-footer styles into here */
#presetModal .kms-modal-footer {
    background-color: var(--kms-primary-light);
    border-top: 1px solid var(--kms-border-color);
    padding: 6px 12px;
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
}


/* =================================== */
/* ===== Modal: Form Controls     ===== */
/* =================================== */

#presetModal .form-grid {
    display: grid;
    grid-template-columns: repeat(12, 1fr);
    gap: 0.75rem 1rem;
}

#presetModal .col-12 { grid-column: span 12; }
#presetModal .col-6 { grid-column: span 6; }
#presetModal .col-4 { grid-column: span 4; }

#presetModal .kms-label {
    display: block;
    font-weight: 600;
    color: var(--kms-text-label);
    margin-bottom: 0.35rem;
}

#presetModal .kms-input,
#presetModal .kms-select,
#presetModal .kms-textarea {
    width: 100%;
    padding: 8px 10px;
    border: 1px solid var(--kms-text-light);
    border-radius: 10px;
    background: var(--kms-secondary);
    color: var(--kms-text-light);
    margin-bottom: 3px;
}

#presetModal .kms-input:focus,
#presetModal .kms-select:focus,
#presetModal .kms-textarea:focus {
    outline: none;
    border-color: var(--kms-border-focus);
    box-shadow: 0 0 0 0.2rem var(--kms-link-bg);
}

#presetModal .toolbar-row {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

#presetModal .toolbar-row .grow {
    flex: 1 1 auto;
}


/* =================================== */
/* ===== Modal: Preset List       ===== */
/* =================================== */

#presetModal #presetList {
    min-height: 400px;
    max-height: 60vh;
    overflow-y: auto;
    padding: 0.25rem;
}

#presetModal .preset-item {
    display: grid;
    grid-template-columns: 40px 1fr auto;
    gap: 0.75rem;
    align-items: center;
    border: 1px solid var(--kms-border-light);
    border-radius: 12px;
    padding: 3px 10px;
    background: #ffcf6b;
    margin-bottom: 2px;
    height: auto; /* Override potential global styles */
}

#presetModal .drag-handle {
    cursor: grab;
    color: #adb5bd;
}

#presetModal .drag-handle:active {
    cursor: grabbing;
}

#presetModal .preset-item-main {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

#presetModal .preset-title-line {
    display: flex;
    flex-wrap: wrap;
    align-items: baseline;
    gap: 0.4rem 0.6rem;
}

#presetModal .preset-name {
    font-weight: 600;
    color: var(--kms-text-dark);
}

#presetModal .preset-category {
    color: var(--kms-link);
}

#presetModal .preset-desc {
    color: var(--kms-text-muted);
}

#presetModal .preset-price-line {
    display: flex;
    align-items: center;
    gap: 0.6rem;
}

#presetModal .price-original {
    color: #00b6ff;
    text-decoration: line-through;
    font-size: 20px;
}

#presetModal .price-special {
    display: inline-block;
    background: #ff8d00;
    color: var(--kms-text-light);
    padding: 3px 8px;
    border-radius: 999px;
    font-weight: 600;
}

#presetModal .price-discount {
    color: #ff0000;
    font-size: 20px;
    font-weight: 600;
}

#presetModal .price-default {
    display: inline-block;
    background: #ffbc00;
    color: var(--kms-text-light);
    padding: 3px 8px;
    border-radius: 999px;
    font-weight: 600;
}

#presetModal .preset-item-actions {
    display: inline-flex;
    align-items: center;
    gap: 0.4rem;
}

.preset-empty {
    text-align: center;
    color: var(--kms-text-muted);
    padding: 1rem;
}

/* =================================== */
/* ===== Modal: Other Content     ===== */
/* =================================== */

/* Logo Preview Modal */
.logo-preview {
    border: 1px solid var(--kms-border-color);
    border-radius: 8px;
    background: var(--kms-bg-light);
}
#logoPreviewImage {
    max-height: 200px;
    object-fit: contain;
    border: 1px solid var(--kms-border-color);
    border-radius: 4px;
}
#logoPreview { display: none; /* Default hidden */ }

/* Configuration Sections */
.config-section {
    margin-bottom: 2rem;
    padding: 1rem;
    border: 1px solid var(--kms-border-color);
    border-radius: 8px;
    background: var(--kms-bg-light);
}
.config-section h6 {
    color: var(--kms-link);
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--kms-link);
}

/* =================================================================
   ADD ITEM MODAL REDESIGN - MODERN & BEAUTIFUL
   ================================================================= */

/* Modal Content */
.add-item-modal-content {
    border: none;
    border-radius: 24px;
    box-shadow:
        0 25px 80px rgba(0, 0, 0, 0.15),
        0 0 0 1px rgba(255, 255, 255, 0.05);
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    overflow: hidden;
}

/* Modal Header */
.add-item-modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    padding: 2rem 2.5rem 1.5rem;
    position: relative;
    overflow: hidden;
}

.add-item-modal-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.modal-title-container {
    display: flex;
    align-items: center;
    gap: 1rem;
    position: relative;
    z-index: 1;
}

.modal-icon {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.add-item-modal-header .modal-title {
    color: white;
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.add-item-modal-close {
    position: absolute;
    top: 1.5rem;
    right: 2rem;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 12px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 18px;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    z-index: 2;
}

.add-item-modal-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* Modal Body */
.add-item-modal-body {
    padding: 2.5rem;
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
}

/* Form Sections */
.form-section {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.section-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
    font-size: 1.1rem;
    font-weight: 600;
    color: #374151;
    padding-bottom: 0.75rem;
    border-bottom: 2px solid #f3f4f6;
}

.section-title .icon-symbol {
    font-size: 1.2rem;
}

/* Form Labels */
.add-item-form .form-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
}

.add-item-form .form-label .icon-symbol {
    font-size: 1rem;
    opacity: 0.8;
}

.required {
    color: #ef4444;
    font-weight: 700;
}

/* Modern Inputs */
.modern-input,
.modern-select,
.modern-textarea {
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    padding: 0.75rem 1rem;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    background: white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.modern-input:focus,
.modern-select:focus,
.modern-textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow:
        0 0 0 3px rgba(102, 126, 234, 0.1),
        0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.modern-textarea {
    resize: vertical;
    min-height: 80px;
}

/* Input Groups */
.input-group .input-group-text {
    background: #f8fafc;
    border: 2px solid #e5e7eb;
    border-right: none;
    border-radius: 12px 0 0 12px;
    font-weight: 600;
    color: #6b7280;
}

.input-group .modern-input {
    border-left: none;
    border-radius: 0 12px 12px 0;
}

.input-group:focus-within .input-group-text {
    border-color: #667eea;
    background: #f0f4ff;
    color: #667eea;
}

/* Form Check */
.form-check {
    padding: 1rem;
    background: #f8fafc;
    border-radius: 12px;
    border: 2px solid #e5e7eb;
    transition: all 0.3s ease;
}

.form-check:hover {
    background: #f0f4ff;
    border-color: #c7d2fe;
}

.form-check-input:checked {
    background-color: #667eea;
    border-color: #667eea;
}

.form-check-label {
    font-weight: 500;
    color: #374151;
    cursor: pointer;
}

/* Modal Footer */
.add-item-modal-content .modal-footer {
    background: #f8fafc;
    border: none;
    padding: 1.5rem 2.5rem;
    gap: 1rem;
    justify-content: flex-end;
}

.add-item-modal-content .modal-footer .btn {
    border-radius: 12px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    border: none;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.add-item-modal-content .modal-footer .btn-secondary {
    background: #6b7280;
    color: white;
}

.add-item-modal-content .modal-footer .btn-secondary:hover {
    background: #4b5563;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(107, 114, 128, 0.3);
}

.add-item-modal-content .modal-footer .btn-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
}

.add-item-modal-content .modal-footer .btn-success:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
    .add-item-modal-header {
        padding: 1.5rem 1.5rem 1rem;
    }

    .add-item-modal-body {
        padding: 1.5rem;
    }

    .form-section {
        padding: 1rem;
    }

    .modal-title-container {
        gap: 0.75rem;
    }

    .modal-icon {
        width: 50px;
        height: 50px;
        font-size: 24px;
    }

    .add-item-modal-header .modal-title {
        font-size: 1.25rem;
    }
}

/* Loading & Error States */
.modal-loading { display: flex; justify-content: center; align-items: center; padding: 3rem; }
.modal-loading .spinner-border { width: 3rem; height: 3rem; }
.modal-error { text-align: center; padding: 2rem; color: var(--kms-danger); }
.modal-error i { font-size: 3rem; margin-bottom: 1rem; }

/* Confirmation Dialog */
.confirmation-dialog .modal-body { text-align: center; padding: 2rem; }
.confirmation-dialog .modal-body i { font-size: 3rem; color: #ffc107; margin-bottom: 1rem; }

/* Status Icons */
.modal-icon-success { color: var(--kms-success); font-size: 1.25rem; }
.modal-icon-error   { color: var(--kms-danger); font-size: 1.25rem; }
.modal-icon-warning { color: #ffc107; font-size: 1.25rem; }
.modal-icon-info    { color: #0dcaf0; font-size: 1.25rem; }


/* =================================== */
/* ===== Reusable Buttons         ===== */
/* =================================== */

/* Golden Select Preset Button */
.btn-select-preset {
    background: linear-gradient(135deg, var(--kms-gold-start) 0%, var(--kms-gold-mid) 50%, var(--kms-gold-end) 100%);
    border: 2px solid var(--kms-gold-border);
    color: var(--kms-gold-text);
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.3);
    box-shadow: 0 4px 8px rgba(255, 215, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.4);
    transition: all 0.3s ease;
    border-radius: 8px;
    padding: 0.5rem 1rem;
}
.btn-select-preset:hover {
    background: linear-gradient(135deg, var(--kms-gold-hover-start) 0%, var(--kms-gold-hover-mid) 50%, var(--kms-gold-hover-end) 100%);
    border-color: var(--kms-gold-hover-border);
    color: var(--kms-gold-hover-text);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(255, 215, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.5);
}
.btn-select-preset:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(255, 215, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.3);
}
.btn-select-preset i { margin-right: 0.5rem; }

/* Enhanced Action Buttons for Receipt Items */
.btn-action {
    background: var(--btn-bg, var(--kms-bg-widget));
    color: var(--btn-color, var(--kms-text-dark));
    border: 2px solid var(--btn-border, var(--kms-border-light));
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    line-height: 1.2;
    display: inline-flex;
    align-items: center;
    gap: 0.375rem;
    border-radius: 12px;
    font-weight: 500;
    transition: all 0.2s ease;
    cursor: pointer;
    min-height: 36px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
.btn-action:hover {
    filter: brightness(0.95);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}
.btn-action .icon-symbol { font-size: 1em; }
.btn-action .btn-text { display: none; }

.btn-action-select { --btn-bg: #e8f7e9; --btn-color: #198754; --btn-border: #198754; }
.btn-action-edit   { --btn-bg: #e7f1ff; --btn-color: #0d6efd; --btn-border: #0d6efd; }
.btn-action-delete { --btn-bg: #fbe9eb; --btn-color: #dc3545; --btn-border: #dc3545; }
.btn-action-toggle { --btn-bg: #fff3cd; --btn-color: #856404; --btn-border: #ffc107; }

/* Enhanced hover effects for action buttons */
.btn-action-edit:hover   { --btn-bg: #cfe2ff; --btn-border: #0a58ca; }
.btn-action-delete:hover { --btn-bg: #f5c2c7; --btn-border: #b02a37; }
.btn-action-toggle:hover { --btn-bg: #ffecb5; --btn-border: #ffca2c; }


/* =================================== */
/* ===== Quick Nav & Z-index      ===== */
/* =================================== */

.quick-nav {
    position: fixed;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    z-index: 999;
}
.quick-nav a {
    width: 40px; height: 40px;
    display: grid;
    place-items: center;
    background: rgba(255, 255, 255, 0.95);
    color: var(--kms-link);
    border: 1px solid var(--kms-border-light);
    border-radius: 999px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transition: all 0.15s ease;
    backdrop-filter: blur(10px);
}
.quick-nav a:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

/* Ensure content isn't under the fixed nav */
.quicknav-safe { padding-right: 0; }
@media (min-width: 992px) {
    .quicknav-safe { padding-right: 60px; }
}

/* Language Dropdown - High z-index */
.navbar .dropdown-menu { z-index: 9999 !important; }
.navbar .nav-item.dropdown { position: relative; z-index: 9998; }


/* =================================== */
/* ===== Animations               ===== */
/* =================================== */

@keyframes slideInRight {
    from { opacity: 0; transform: translateX(100%); }
    to { opacity: 1; transform: translateX(0); }
}

@keyframes slideOutRight {
    from { opacity: 1; transform: translateX(0); }
    to { opacity: 0; transform: translateX(100%); }
}


/* =================================== */
/* ===== Responsive Adjustments   ===== */
/* =================================== */

@media (max-width: 768px) {
    #presetModal .kms-modal-dialog { margin: 0.5rem; }
    #presetModal .col-6,
    #presetModal .col-4 {
        grid-column: span 12;
    }
    .price-inputs { grid-template-columns: 1fr; }
    .toast-message { left: 10px; right: 10px; min-width: auto; }
}

@media (max-width: 991px) {
    .quick-nav { display: none; }
}

@media (min-width: 576px) {
    .btn-action .btn-text {
        display: inline;
        margin-left: 0.15rem;
    }
}