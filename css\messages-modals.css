/**
 * ===================================================================
 * Messages and Modals Styles
 * Toast messages, alerts, and modal dialogs
 * ===================================================================
 */

/* =================================== */
/* ===== Core & Variables         ===== */
/* =================================== */

:root {
    /* Brand Colors */
    --kms-primary: #00a5ff;
    --kms-primary-light: #1fb5db;
    --kms-secondary: #00a16d;
    --kms-link: #0d6efd;
    --kms-link-bg: rgba(13, 110, 253, 0.15);

    /* Text Colors */
    --kms-text-light: #ffffff;
    --kms-text-dark: #212529;
    --kms-text-muted: #6c757d;
    --kms-text-label: #495057;

    /* Background & Border Colors */
    --kms-bg-light: #f8f9fa;
    --kms-bg-widget: #f1f3f5;
    --kms-border-color: #dee2e6;
    --kms-border-light: #e9ecef;
    --kms-border-focus: #0d6efd;

    /* Semantic Colors */
    --kms-success: #0f5132;
    --kms-success-bg: #d1e7dd;
    --kms-success-border: #badbcc;
    --kms-info: #055160;
    --kms-info-bg: #d1ecf1;
    --kms-info-border: #bee5eb;
    --kms-warning: #664d03;
    --kms-warning-bg: #fff3cd;
    --kms-warning-border: #ffecb5;
    --kms-danger: #842029;
    --kms-danger-bg: #f8d7da;
    --kms-danger-border: #f5c2c7;

    /* Golden Button Colors */
    --kms-gold-start: #FFD700;
    --kms-gold-mid: #FFA500;
    --kms-gold-end: #FF8C00;
    --kms-gold-border: #DAA520;
    --kms-gold-text: #8B4513;
    --kms-gold-hover-start: #FFED4E;
    --kms-gold-hover-mid: #FFB347;
    --kms-gold-hover-end: #FF7F50;
    --kms-gold-hover-border: #B8860B;
    --kms-gold-hover-text: #654321;
}

/* Body helper to prevent background scrolling when modal is open */
body.kms-no-scroll {
    overflow: hidden;
}


/* =================================== */
/* ===== Toast Messages & Alerts  ===== */
/* =================================== */

/* Toast Messages */
.toast-message {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    min-width: 300px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-radius: 8px;
    animation: slideInRight 0.3s ease-out;
}

.toast-message.removing {
    animation: slideOutRight 0.3s ease-in;
}

/* Alert Variants */
.alert-success {
    background-color: var(--kms-success-bg);
    border-color: var(--kms-success-border);
    color: var(--kms-success);
}

.alert-info {
    background-color: var(--kms-info-bg);
    border-color: var(--kms-info-border);
    color: var(--kms-info);
}

.alert-warning {
    background-color: var(--kms-warning-bg);
    border-color: var(--kms-warning-border);
    color: var(--kms-warning);
}

.alert-danger {
    background-color: var(--kms-danger-bg);
    border-color: var(--kms-danger-border);
    color: var(--kms-danger);
}


/* =================================== */
/* ===== Pure CSS Modal (Base)    ===== */
/* =================================== */
#presetModal.kms-modal {
    position: fixed;
    inset: 0;
    display: none;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.45);
    z-index: 1050;
}

#presetModal.kms-modal.is-open {
    display: flex;
}

#presetModal .kms-modal-dialog {
    width: min(1200px, 95vw);
    max-height: 90vh;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.35);
    background: var(--kms-text-light);
    display: flex;
    flex-direction: column;
}

/* Merged .modal-header styles into here */
#presetModal .kms-modal-header {
    background: var(--kms-primary);
    color: var(--kms-text-light);
    padding: 6px 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: none;
}

/* Merged .modal-title styles into here */
#presetModal .kms-modal-title {
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0;
}

#presetModal .kms-modal-close {
    appearance: none;
    border: 0;
    background: transparent;
    color: var(--kms-text-light);
    font-size: 1.25rem;
    line-height: 1;
    padding: 0.25rem;
    border-radius: 6px;
    cursor: pointer;
    filter: invert(1);
}

#presetModal .kms-modal-close:hover {
    background: rgba(255, 255, 255, 0.15);
}

/* Merged .modal-body styles into here */
#presetModal .kms-modal-body {
    padding: 1rem;
    overflow: auto;
    background-color: var(--kms-primary-light);
}

/* Merged .modal-footer styles into here */
#presetModal .kms-modal-footer {
    background-color: var(--kms-primary-light);
    border-top: 1px solid var(--kms-border-color);
    padding: 6px 12px;
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
}


/* =================================== */
/* ===== Modal: Form Controls     ===== */
/* =================================== */

#presetModal .form-grid {
    display: grid;
    grid-template-columns: repeat(12, 1fr);
    gap: 0.75rem 1rem;
}

#presetModal .col-12 { grid-column: span 12; }
#presetModal .col-6 { grid-column: span 6; }
#presetModal .col-4 { grid-column: span 4; }

#presetModal .kms-label {
    display: block;
    font-weight: 600;
    color: var(--kms-text-label);
    margin-bottom: 0.35rem;
}

#presetModal .kms-input,
#presetModal .kms-select,
#presetModal .kms-textarea {
    width: 100%;
    padding: 8px 10px;
    border: 1px solid var(--kms-text-light);
    border-radius: 10px;
    background: var(--kms-secondary);
    color: var(--kms-text-light);
    margin-bottom: 3px;
}

#presetModal .kms-input:focus,
#presetModal .kms-select:focus,
#presetModal .kms-textarea:focus {
    outline: none;
    border-color: var(--kms-border-focus);
    box-shadow: 0 0 0 0.2rem var(--kms-link-bg);
}

#presetModal .toolbar-row {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

#presetModal .toolbar-row .grow {
    flex: 1 1 auto;
}


/* =================================== */
/* ===== Modal: Preset List       ===== */
/* =================================== */

#presetModal #presetList {
    min-height: 400px;
    max-height: 60vh;
    overflow-y: auto;
    padding: 0.25rem;
}

#presetModal .preset-item {
    display: grid;
    grid-template-columns: 40px 1fr auto;
    gap: 0.75rem;
    align-items: center;
    border: 1px solid var(--kms-border-light);
    border-radius: 12px;
    padding: 3px 10px;
    background: #ffcf6b;
    margin-bottom: 2px;
    height: auto; /* Override potential global styles */
}

#presetModal .drag-handle {
    cursor: grab;
    color: #adb5bd;
}

#presetModal .drag-handle:active {
    cursor: grabbing;
}

#presetModal .preset-item-main {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

#presetModal .preset-title-line {
    display: flex;
    flex-wrap: wrap;
    align-items: baseline;
    gap: 0.4rem 0.6rem;
}

#presetModal .preset-name {
    font-weight: 600;
    color: var(--kms-text-dark);
}

#presetModal .preset-category {
    color: var(--kms-link);
}

#presetModal .preset-desc {
    color: var(--kms-text-muted);
}

#presetModal .preset-price-line {
    display: flex;
    align-items: center;
    gap: 0.6rem;
}

#presetModal .price-original {
    color: #00b6ff;
    text-decoration: line-through;
    font-size: 20px;
}

#presetModal .price-special {
    display: inline-block;
    background: #ff8d00;
    color: var(--kms-text-light);
    padding: 3px 8px;
    border-radius: 999px;
    font-weight: 600;
}

#presetModal .price-discount {
    color: #ff0000;
    font-size: 20px;
    font-weight: 600;
}

#presetModal .price-default {
    display: inline-block;
    background: #ffbc00;
    color: var(--kms-text-light);
    padding: 3px 8px;
    border-radius: 999px;
    font-weight: 600;
}

#presetModal .preset-item-actions {
    display: inline-flex;
    align-items: center;
    gap: 0.4rem;
}

.preset-empty {
    text-align: center;
    color: var(--kms-text-muted);
    padding: 1rem;
}

/* =================================== */
/* ===== Modal: Other Content     ===== */
/* =================================== */

/* Logo Preview Modal */
.logo-preview {
    border: 1px solid var(--kms-border-color);
    border-radius: 8px;
    background: var(--kms-bg-light);
}
#logoPreviewImage {
    max-height: 200px;
    object-fit: contain;
    border: 1px solid var(--kms-border-color);
    border-radius: 4px;
}
#logoPreview { display: none; /* Default hidden */ }

/* Configuration Sections */
.config-section {
    margin-bottom: 2rem;
    padding: 1rem;
    border: 1px solid var(--kms-border-color);
    border-radius: 8px;
    background: var(--kms-bg-light);
}
.config-section h6 {
    color: var(--kms-link);
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--kms-link);
}

/* Add Item Modal Form */
.add-item-form .form-group { margin-bottom: 1rem; }
.add-item-form .form-label { font-weight: 600; color: var(--kms-text-label); }
.price-inputs { display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; }

/* Loading & Error States */
.modal-loading { display: flex; justify-content: center; align-items: center; padding: 3rem; }
.modal-loading .spinner-border { width: 3rem; height: 3rem; }
.modal-error { text-align: center; padding: 2rem; color: var(--kms-danger); }
.modal-error i { font-size: 3rem; margin-bottom: 1rem; }

/* Confirmation Dialog */
.confirmation-dialog .modal-body { text-align: center; padding: 2rem; }
.confirmation-dialog .modal-body i { font-size: 3rem; color: #ffc107; margin-bottom: 1rem; }

/* Status Icons */
.modal-icon-success { color: var(--kms-success); font-size: 1.25rem; }
.modal-icon-error   { color: var(--kms-danger); font-size: 1.25rem; }
.modal-icon-warning { color: #ffc107; font-size: 1.25rem; }
.modal-icon-info    { color: #0dcaf0; font-size: 1.25rem; }


/* =================================== */
/* ===== Reusable Buttons         ===== */
/* =================================== */

/* Golden Select Preset Button */
.btn-select-preset {
    background: linear-gradient(135deg, var(--kms-gold-start) 0%, var(--kms-gold-mid) 50%, var(--kms-gold-end) 100%);
    border: 2px solid var(--kms-gold-border);
    color: var(--kms-gold-text);
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.3);
    box-shadow: 0 4px 8px rgba(255, 215, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.4);
    transition: all 0.3s ease;
    border-radius: 8px;
    padding: 0.5rem 1rem;
}
.btn-select-preset:hover {
    background: linear-gradient(135deg, var(--kms-gold-hover-start) 0%, var(--kms-gold-hover-mid) 50%, var(--kms-gold-hover-end) 100%);
    border-color: var(--kms-gold-hover-border);
    color: var(--kms-gold-hover-text);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(255, 215, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.5);
}
.btn-select-preset:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(255, 215, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.3);
}
.btn-select-preset i { margin-right: 0.5rem; }

/* Enhanced Action Buttons for Receipt Items */
.btn-action {
    background: var(--btn-bg, var(--kms-bg-widget));
    color: var(--btn-color, var(--kms-text-dark));
    border: 2px solid var(--btn-border, var(--kms-border-light));
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    line-height: 1.2;
    display: inline-flex;
    align-items: center;
    gap: 0.375rem;
    border-radius: 12px;
    font-weight: 500;
    transition: all 0.2s ease;
    cursor: pointer;
    min-height: 36px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
.btn-action:hover {
    filter: brightness(0.95);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}
.btn-action .icon-symbol { font-size: 1em; }
.btn-action .btn-text { display: none; }

.btn-action-select { --btn-bg: #e8f7e9; --btn-color: #198754; --btn-border: #198754; }
.btn-action-edit   { --btn-bg: #e7f1ff; --btn-color: #0d6efd; --btn-border: #0d6efd; }
.btn-action-delete { --btn-bg: #fbe9eb; --btn-color: #dc3545; --btn-border: #dc3545; }
.btn-action-toggle { --btn-bg: #fff3cd; --btn-color: #856404; --btn-border: #ffc107; }

/* Enhanced hover effects for action buttons */
.btn-action-edit:hover   { --btn-bg: #cfe2ff; --btn-border: #0a58ca; }
.btn-action-delete:hover { --btn-bg: #f5c2c7; --btn-border: #b02a37; }
.btn-action-toggle:hover { --btn-bg: #ffecb5; --btn-border: #ffca2c; }


/* =================================== */
/* ===== Quick Nav & Z-index      ===== */
/* =================================== */

.quick-nav {
    position: fixed;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    z-index: 999;
}
.quick-nav a {
    width: 40px; height: 40px;
    display: grid;
    place-items: center;
    background: rgba(255, 255, 255, 0.95);
    color: var(--kms-link);
    border: 1px solid var(--kms-border-light);
    border-radius: 999px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transition: all 0.15s ease;
    backdrop-filter: blur(10px);
}
.quick-nav a:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

/* Ensure content isn't under the fixed nav */
.quicknav-safe { padding-right: 0; }
@media (min-width: 992px) {
    .quicknav-safe { padding-right: 60px; }
}

/* Language Dropdown - High z-index */
.navbar .dropdown-menu { z-index: 9999 !important; }
.navbar .nav-item.dropdown { position: relative; z-index: 9998; }


/* =================================== */
/* ===== Animations               ===== */
/* =================================== */

@keyframes slideInRight {
    from { opacity: 0; transform: translateX(100%); }
    to { opacity: 1; transform: translateX(0); }
}

@keyframes slideOutRight {
    from { opacity: 1; transform: translateX(0); }
    to { opacity: 0; transform: translateX(100%); }
}


/* =================================== */
/* ===== Responsive Adjustments   ===== */
/* =================================== */

@media (max-width: 768px) {
    #presetModal .kms-modal-dialog { margin: 0.5rem; }
    #presetModal .col-6,
    #presetModal .col-4 {
        grid-column: span 12;
    }
    .price-inputs { grid-template-columns: 1fr; }
    .toast-message { left: 10px; right: 10px; min-width: auto; }
}

@media (max-width: 991px) {
    .quick-nav { display: none; }
}

@media (min-width: 576px) {
    .btn-action .btn-text {
        display: inline;
        margin-left: 0.15rem;
    }
}