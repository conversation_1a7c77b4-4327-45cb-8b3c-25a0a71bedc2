/**
 * Receipt Generator
 * Handles receipt generation, HTML creation, and preview functionality
 */

class ReceiptGenerator {
    constructor() {
        this.defaultPaymentMethod = 'cash';
    }

    /**
     * Generate Receipt
     */
    generateReceipt() {
        const receiptItems = window.ItemManager ? window.ItemManager.getReceiptItems() : [];

        if (receiptItems.length === 0) {
            if (typeof UIManager !== 'undefined') {
                const message = window.LanguageManager ?
                    window.LanguageManager.getText('error_no_items_to_generate') :
                    'Please add items to the receipt first';
                UIManager.showMessage(message, 'warning');
            }
            return;
        }

        // Auto-generate customer info if empty and fill the form fields
        let customerName = document.getElementById('customerName')?.value.trim();
        if (!customerName) {
            customerName = this.generateCustomerName();
            if (document.getElementById('customerName')) {
                document.getElementById('customerName').value = customerName;
            }
        }

        let customerPhone = document.getElementById('customerPhone')?.value.trim();
        if (!customerPhone) {
            customerPhone = this.generateCustomerPhone();
            if (document.getElementById('customerPhone')) {
                document.getElementById('customerPhone').value = customerPhone;
            }
        }

        let customerEmail = document.getElementById('customerEmail')?.value.trim();
        if (!customerEmail) {
            customerEmail = this.generateCustomerEmail();
            if (document.getElementById('customerEmail')) {
                document.getElementById('customerEmail').value = customerEmail;
            }
        }

        // Collect receipt data
        const receiptData = {
            customer: {
                name: customerName,
                phone: customerPhone,
                email: customerEmail,
                address: document.getElementById('customerAddress')?.value.trim() || ''
            },
            paymentMethod: this.defaultPaymentMethod, // Use default since form field was removed
            items: receiptItems.map(item => ({
                name: item.name,
                category: item.category,
                description: item.description,
                quantity: item.quantity,
                unitPrice: item.unitPrice,
                totalPrice: item.totalPrice,
                hidePrice: item.hidePrice || false
            })),
            totals: window.ItemManager ? window.ItemManager.calculateTotals() : this.calculateTotals(receiptItems),
            notes: document.getElementById('notes')?.value.trim() || '',
            receiptNumber: document.getElementById('receiptNumber')?.value || 'KMS-UltraVIP-0000001'
            // Removed receiptDate - no longer needed
        };

        // Generate receipt HTML and display in preview area
        const receiptHtml = this.generateReceiptHtml(receiptData);
        this.displayReceiptPreview(receiptHtml);

        // Show action buttons
        const previewActions = document.getElementById('previewActions');
        if (previewActions) {
            previewActions.classList.remove('d-none');
        }

        if (typeof UIManager !== 'undefined') {
            UIManager.showMessage('Receipt generated successfully!', 'success');
        }
    }

    /**
     * Generate Receipt HTML
     */
    generateReceiptHtml(data) {
        const formatCurrency = (amount) => {
            return new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD',
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
            }).format(amount);
        };

        return `
            <div class="receipt-container">
                <div class="receipt-inner">
                    ${window.currentLogo && window.currentLogo.src ? `
                    <div class="receipt-logo">
                        <img src="${window.currentLogo.src}" alt="Company Logo">
                    </div>
                    ` : ''}

                    <div class="preview-receipt-header">
                        <div class="receipt-title">${window.LanguageManager ? window.LanguageManager.getText('company_name') : 'KelvinKMS'}</div>
                        <div class="receipt-company-info">
                            ${window.LanguageManager ? window.LanguageManager.getText('company_website') : 'KelvinKMS.com'}<br>
                            ${window.LanguageManager ? window.LanguageManager.getText('company_phone') : '************'}<br>
                            ${window.LanguageManager ? window.LanguageManager.getText('company_email') : '<EMAIL>'}
                        </div>
                    </div>

                    <div class="preview-receipt-info">
                        <div>
                            <div class="receipt-number">
                                Receipt Number: ${data.receiptNumber}
                            </div>
                        </div>
                    </div>

                    <div class="preview-customer-info">
                        <h6>Customer Information</h6>
                        <div class="customer-field">
                            <span class="customer-field-label">Name:</span>
                            <span class="customer-field-value">${data.customer.name || ''}</span>
                        </div>
                        <div class="customer-field">
                            <span class="customer-field-label">Phone:</span>
                            <span class="customer-field-value">${data.customer.phone || ''}</span>
                        </div>
                        <div class="customer-field">
                            <span class="customer-field-label">Email:</span>
                            <span class="customer-field-value">${data.customer.email || ''}</span>
                        </div>
                        <div class="customer-field">
                            <span class="customer-field-label">Address:</span>
                            <span class="customer-field-value">${data.customer.address || ''}</span>
                        </div>
                    </div>

                    <div class="receipt-items">
                        <div class="items-header">
                            <h6>Items (Total: ${data.items.length} items)</h6>
                        </div>
                        <table class="receipt-table">
                            <thead>
                                <tr>
                                    <th class="text-center receipt-table-header-number-wide">#</th>
                                    <th class="text-left">Item</th>
                                    <th class="text-right">Original Price</th>
                                    <th class="text-right">Discount</th>
                                    <th class="text-right">Final Price</th>
                                    <th class="text-center">Qty</th>
                                    <th class="text-right">Total Price</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${data.items.map((item, index) => {
            const originalPrice = item.originalPrice || item.unitPrice;
            const specialPrice = item.specialPrice || item.unitPrice;
            const discountPercent = item.discountPercent || 0;
            const actualFinalPrice = item.hidePrice ? 0 : specialPrice;

            return `
                                    <tr>
                                        <td class="text-center item-number">${index + 1}</td>
                                        <td class="text-left">
                                            <div class="item-name">${item.name}</div>
                                            ${item.description ? `<div class="item-description">${item.description}</div>` : ''}
                                        </td>
                                        <td class="text-right">${item.hidePrice ? 'N/A' : formatCurrency(originalPrice)}</td>
                                        <td class="text-right">${item.hidePrice ? 'N/A' : (discountPercent > 0 ? discountPercent + '%' : '-')}</td>
                                        <td class="text-right">${item.hidePrice ? 'N/A' : formatCurrency(actualFinalPrice)}</td>
                                        <td class="text-center">${item.quantity}</td>
                                        <td class="text-right">${item.hidePrice ? 'N/A' : formatCurrency(item.totalPrice)}</td>
                                    </tr>
                                `;
        }).join('')}
                            </tbody>
                        </table>
                    </div>

                    <div class="receipt-totals">
                        <table class="totals-table">
                            <tr>
                                <td class="label">Subtotal:</td>
                                <td class="amount">${formatCurrency(data.totals.subtotal)}</td>
                            </tr>
                            ${data.totals.discount > 0 ? `
                            <tr>
                                <td class="label">Discount:</td>
                                <td class="amount">-${formatCurrency(data.totals.discount)}</td>
                            </tr>
                            ` : ''}
                            ${data.totals.tax > 0 ? `
                            <tr>
                                <td class="label">Tax:</td>
                                <td class="amount">${formatCurrency(data.totals.tax)}</td>
                            </tr>
                            ` : ''}
                            <tr class="total-row">
                                <td class="label">Total:</td>
                                <td class="amount">${formatCurrency(data.totals.total)}</td>
                            </tr>
                        </table>
                    </div>

                    <div class="payment-method">
                        <div class="payment-options">
                            ${['Cash', 'Venmo', 'Zelle', 'Square', 'Stripe'].map(pm => {
            return `
                                <div class="payment-option-button">
                                    <div class="payment-checkbox"></div>
                                    <span class="payment-label">${pm}</span>
                                </div>
                                `;
        }).join('')}
                        </div>
                    </div>

                    ${data.notes ? `
                    <div class="receipt-notes">
                        <h6>Notes</h6>
                        <p>${data.notes}</p>
                    </div>
                    ` : ''}

                    <div class="signature-section">
                        <div class="signature-labels-row">
                            <div class="signature-label-item">
                                <span class="signature-label">Seller Signature:</span>
                            </div>
                            <div class="signature-label-item">
                                <span class="signature-label">Buyer Signature:</span>
                            </div>
                            <div class="signature-label-item">
                                <span class="signature-label">Signature Date:</span>
                            </div>
                        </div>
                        <div class="signature-lines-area">
                            <div class="signature-line-space"></div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Display Receipt Preview
     */
    displayReceiptPreview(html) {
        const previewElement = document.getElementById('receiptPreview');
        if (previewElement) {
            previewElement.innerHTML = html;
            previewElement.classList.add('has-content');
        }
    }

    /**
     * Update Receipt Preview
     */
    updateReceiptPreview() {
        console.log('updateReceiptPreview called');

        // This function can be called to refresh the preview
        const receiptItems = window.ItemManager ? window.ItemManager.getReceiptItems() : [];
        console.log('Receipt items for preview:', receiptItems);

        if (receiptItems.length > 0) {
            // Get current customer info without auto-generation
            const customerName = document.getElementById('customerName')?.value.trim() || '';
            const customerPhone = document.getElementById('customerPhone')?.value.trim() || '';
            const customerEmail = document.getElementById('customerEmail')?.value.trim() || '';

            // Collect receipt data without auto-generation
            const receiptData = {
                customer: {
                    name: customerName,
                    phone: customerPhone,
                    email: customerEmail,
                    address: document.getElementById('customerAddress')?.value.trim() || ''
                },
                paymentMethod: this.defaultPaymentMethod,
                items: receiptItems.map(item => ({
                    name: item.name,
                    category: item.category,
                    description: item.description,
                    quantity: item.quantity,
                    unitPrice: item.unitPrice,
                    totalPrice: item.totalPrice,
                    originalPrice: item.originalPrice || 0,
                    specialPrice: item.specialPrice || item.unitPrice,
                    discountPercent: item.discountPercent || 0,
                    hidePrice: item.hidePrice || false
                })),
                totals: window.ItemManager ? window.ItemManager.calculateTotals() : this.calculateTotals(receiptItems),
                notes: document.getElementById('notes')?.value.trim() || '',
                receiptNumber: document.getElementById('receiptNumber')?.value || 'KMS-UltraVIP-0000001'
            };

            console.log('Receipt data for preview:', receiptData);

            // Generate receipt HTML and display in preview area
            const receiptHtml = this.generateReceiptHtml(receiptData);
            this.displayReceiptPreview(receiptHtml);

            console.log('Receipt preview updated successfully');
        } else {
            // Clear preview if no items
            const previewElement = document.getElementById('receiptPreview');
            if (previewElement) {
                previewElement.innerHTML = `
                    <div class="text-center text-muted">
                        <i class="fas fa-file-invoice fa-3x mb-3"></i>
                        <p>Receipt preview will be shown here with beautiful certificate border</p>
                    </div>
                `;
            }
        }
    }

    /**
     * Calculate Totals (fallback method)
     */
    calculateTotals(items) {
        const subtotal = items.reduce((sum, item) => sum + item.totalPrice, 0);
        const discountAmount = parseFloat(document.getElementById('discountAmount')?.value) || 0;
        const taxRate = parseFloat(document.getElementById('taxRate')?.value) || 0;

        const discountedSubtotal = Math.max(0, subtotal - discountAmount);
        const taxAmount = discountedSubtotal * (taxRate / 100);
        const total = discountedSubtotal + taxAmount;

        return {
            subtotal: subtotal,
            discount: discountAmount,
            tax: taxAmount,
            total: total
        };
    }

    /**
     * Generate Customer Name
     */
    generateCustomerName() {
        const firstNames = ['John', 'Jane', 'Michael', 'Sarah', 'David', 'Lisa', 'Robert', 'Emily', 'James', 'Jessica'];
        const lastNames = ['Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis', 'Rodriguez', 'Martinez'];

        const firstName = firstNames[Math.floor(Math.random() * firstNames.length)];
        const lastName = lastNames[Math.floor(Math.random() * lastNames.length)];

        return `${firstName} ${lastName}`;
    }

    /**
     * Generate Customer Phone
     */
    generateCustomerPhone() {
        const areaCode = Math.floor(Math.random() * 900) + 100;
        const exchange = Math.floor(Math.random() * 900) + 100;
        const number = Math.floor(Math.random() * 9000) + 1000;

        return `(${areaCode}) ${exchange}-${number}`;
    }

    /**
     * Generate Customer Email
     */
    generateCustomerEmail() {
        const domains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 'example.com'];
        const domain = domains[Math.floor(Math.random() * domains.length)];
        const username = 'customer' + Math.floor(Math.random() * 10000);

        return `${username}@${domain}`;
    }

    /**
     * Generate Receipt HTML for Print with Pagination
     * Delegated to PrintManager module
     */
    generateReceiptHtmlForPrint(data) {
        if (window.PrintManager) {
            return window.PrintManager.generateReceiptHtmlForPrint(data);
        } else {
            console.error('PrintManager module not available');
            return '<div>Print functionality not available</div>';
        }
    }

    /**
     * Print Receipt
     * Delegated to PrintManager module
     */
    printReceipt() {
        if (window.PrintManager) {
            return window.PrintManager.printReceipt();
        } else {
            console.error('PrintManager module not available');
            if (typeof UIManager !== 'undefined') {
                UIManager.showMessage('Print functionality not available', 'error');
            }
        }
    }




    

    /**
     * Save Receipt
     */
    async saveReceipt() {
        const receiptItems = window.ItemManager ? window.ItemManager.getReceiptItems() : [];

        if (receiptItems.length === 0) {
            if (typeof UIManager !== 'undefined') {
                const message = window.LanguageManager ?
                    window.LanguageManager.getText('error_no_items_to_save') :
                    'Please add items to the receipt first';
                UIManager.showMessage(message, 'warning');
            }
            return;
        }

        // Auto-generate customer info if empty and fill the form fields
        let customerName = document.getElementById('customerName')?.value.trim();
        if (!customerName) {
            customerName = this.generateCustomerName();
            if (document.getElementById('customerName')) {
                document.getElementById('customerName').value = customerName;
            }
        }

        let customerPhone = document.getElementById('customerPhone')?.value.trim();
        if (!customerPhone) {
            customerPhone = this.generateCustomerPhone();
            if (document.getElementById('customerPhone')) {
                document.getElementById('customerPhone').value = customerPhone;
            }
        }

        let customerEmail = document.getElementById('customerEmail')?.value.trim();
        if (!customerEmail) {
            customerEmail = this.generateCustomerEmail();
            if (document.getElementById('customerEmail')) {
                document.getElementById('customerEmail').value = customerEmail;
            }
        }

        const receiptData = {
            customer: {
                name: customerName,
                phone: customerPhone,
                email: customerEmail,
                address: document.getElementById('customerAddress')?.value.trim() || ''
            },
            paymentMethod: this.defaultPaymentMethod,
            items: receiptItems.map(item => ({
                name: item.name,
                category: item.category,
                description: item.description,
                quantity: item.quantity,
                unitPrice: item.unitPrice,
                totalPrice: item.totalPrice
            })),
            totals: window.ItemManager ? window.ItemManager.calculateTotals() : this.calculateTotals(receiptItems),
            notes: document.getElementById('notes')?.value.trim() || ''
        };

        try {
            // First save to database
            const response = await fetch('php/save_receipt.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(receiptData)
            });

            const result = await response.json();

            if (result.success) {
                // Generate and download PDF
                this.generatePDF(receiptData, result.receipt_number);

                if (typeof UIManager !== 'undefined') {
                    UIManager.showMessage('Receipt saved successfully and PDF generated!', 'success');
                }
            } else {
                if (typeof UIManager !== 'undefined') {
                    UIManager.showMessage('Failed to save receipt: ' + result.message, 'error');
                }
            }
        } catch (error) {
            console.error('Error saving receipt:', error);
            if (typeof UIManager !== 'undefined') {
                UIManager.showMessage('Error occurred while saving receipt', 'error');
            }
        }
    }

    /**
     * Generate PDF Receipt - Using html2canvas for exact styling
     */
    async generatePDF(receiptData, receiptNumber) {
        if (window.PDFGenerator) {
            return await window.PDFGenerator.generatePDF(receiptData, receiptNumber);
        } else {
            console.error('PDFGenerator module not available');
            if (typeof UIManager !== 'undefined') {
                UIManager.showMessage('PDF generation functionality not available', 'error');
            }
        }
    }
}

// Create global instance
window.ReceiptGenerator = new ReceiptGenerator();

// Export functions for backward compatibility
window.generateReceipt = () => window.ReceiptGenerator.generateReceipt();
window.generateReceiptHtml = (data) => window.ReceiptGenerator.generateReceiptHtml(data);
window.displayReceiptPreview = (html) => window.ReceiptGenerator.displayReceiptPreview(html);
window.updateReceiptPreview = () => window.ReceiptGenerator.updateReceiptPreview();
window.printReceipt = () => window.ReceiptGenerator.printReceipt();
window.saveReceipt = () => window.ReceiptGenerator.saveReceipt();
