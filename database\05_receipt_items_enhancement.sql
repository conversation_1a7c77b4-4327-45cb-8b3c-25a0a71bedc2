-- KMS PC Receipt Maker - Receipt Items Table Enhancement
-- 為 receipt_items 表添加完整的價格信息支持

USE kms_receipt_maker;

-- 檢查並添加缺失的字段到 receipt_items 表
ALTER TABLE receipt_items 
ADD COLUMN IF NOT EXISTS original_price DECIMAL(10,2) NULL COMMENT '原價',
ADD COLUMN IF NOT EXISTS special_price DECIMAL(10,2) NULL COMMENT '特價',
ADD COLUMN IF NOT EXISTS discount_percent INT DEFAULT 0 COMMENT '折扣百分比',
ADD COLUMN IF NOT EXISTS hide_price BOOLEAN DEFAULT FALSE COMMENT '是否隱藏價格';

-- 添加索引以提高查詢性能
ALTER TABLE receipt_items 
ADD INDEX IF NOT EXISTS idx_original_price (original_price),
ADD INDEX IF NOT EXISTS idx_special_price (special_price),
ADD INDEX IF NOT EXISTS idx_discount_percent (discount_percent);

-- 更新現有數據，將 unit_price 複製到 original_price（如果 original_price 為空）
UPDATE receipt_items 
SET original_price = unit_price 
WHERE original_price IS NULL;

-- 顯示表結構確認
DESCRIBE receipt_items;
