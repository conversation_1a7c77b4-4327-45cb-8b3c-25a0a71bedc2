/**
 * Print Manager Module
 * Handles printing functionality for receipts
 */
class PrintManager {
    constructor() {
        this.defaultPaymentMethod = 'Cash';
    }

    /**
     * Generate Receipt HTML for Print with Pagination
     */
    generateReceiptHtmlForPrint(data) {
        const formatCurrency = (amount) => {
            return new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD',
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
            }).format(amount);
        };

        // Enhanced pagination logic to ensure all content is displayed
        const itemsPerPage = 6; // Further reduced to ensure signature section always fits
        const pages = [];

        // Split items into pages
        for (let i = 0; i < data.items.length; i += itemsPerPage) {
            pages.push(data.items.slice(i, i + itemsPerPage));
        }

        // If no items, create one empty page
        if (pages.length === 0) {
            pages.push([]);
        }

        // Generate pages
        const pagesHtml = pages.map((pageItems, pageIndex) => {
            const isLastPage = pageIndex === pages.length - 1;
            const isSummaryPage = pageItems.length === 0 || (isLastPage && pageItems.length <= 3);

            return `
                <div class="receipt-page" style="page-break-after: ${isLastPage ? 'auto' : 'always'};">
                    <div class="receipt-container">
                        <!-- Header Section -->
                        <div class="receipt-header">
                            <div class="company-info">
                                <div class="company-logo-section">
                                    <img src="images/logo.png" alt="Company Logo" class="company-logo" style="max-width: 120px; height: auto;">
                                </div>
                                <div class="company-details">
                                    <h2 class="company-name">KMS PC Receipt Maker</h2>
                                    <div class="company-contact">
                                        <div>📧 <EMAIL></div>
                                        <div>📞 +1 (555) 123-4567</div>
                                        <div>🌐 www.kmspc.com</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="receipt-info">
                                <h3>RECEIPT</h3>
                                <div class="receipt-details">
                                    <div><strong>Receipt #:</strong> ${data.receiptNumber}</div>
                                    <div><strong>Date:</strong> ${new Date().toLocaleDateString()}</div>
                                    <div><strong>Time:</strong> ${new Date().toLocaleTimeString()}</div>
                                    <div><strong>Page:</strong> ${pageIndex + 1} of ${pages.length}</div>
                                </div>
                            </div>
                        </div>

                        <!-- Customer Information -->
                        <div class="customer-section">
                            <h4>Customer Information</h4>
                            <div class="customer-details">
                                <div><strong>Name:</strong> ${data.customer.name || 'N/A'}</div>
                                <div><strong>Phone:</strong> ${data.customer.phone || 'N/A'}</div>
                                <div><strong>Email:</strong> ${data.customer.email || 'N/A'}</div>
                                ${data.customer.address ? `<div><strong>Address:</strong> ${data.customer.address}</div>` : ''}
                            </div>
                        </div>

                        ${!isSummaryPage && pageItems.length > 0 ? `
                        <div class="receipt-items">
                            <table class="receipt-table">
                                <thead>
                                    <tr>
                                        <th class="text-center receipt-table-header-number">#</th>
                                        <th class="text-left">Item</th>
                                        <th class="text-right receipt-table-header-price">Orig. Price</th>
                                        <th class="text-right receipt-table-header-discount">Disc.</th>
                                        <th class="text-right receipt-table-header-price">Final Price</th>
                                        <th class="text-center receipt-table-header-qty">Qty</th>
                                        <th class="text-right receipt-table-header-total">Total Price</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${pageItems.map((item, localIndex) => {
                const globalIndex = pageIndex * itemsPerPage + localIndex + 1;
                const originalPrice = item.originalPrice || item.unitPrice;
                const specialPrice = item.specialPrice || item.unitPrice;
                const discountPercent = item.discountPercent || 0;
                const actualFinalPrice = item.hidePrice ? 0 : specialPrice;

                return `
                                        <tr class="receipt-table-row-compact">
                                            <td class="text-center item-number receipt-table-cell-compact receipt-table-cell-number">${globalIndex}</td>
                                            <td class="text-left receipt-table-cell-compact">
                                                <div class="item-name receipt-table-item-name">${item.name}</div>
                                                ${item.description ? `<div class="item-description receipt-table-item-desc">${item.description}</div>` : ''}
                                            </td>
                                            <td class="text-right receipt-table-cell-compact receipt-table-cell-price">${item.hidePrice ? 'N/A' : formatCurrency(originalPrice)}</td>
                                            <td class="text-right receipt-table-cell-compact receipt-table-cell-price">${item.hidePrice ? 'N/A' : (discountPercent > 0 ? discountPercent + '%' : '-')}</td>
                                            <td class="text-right receipt-table-cell-compact receipt-table-cell-price">${item.hidePrice ? 'N/A' : formatCurrency(actualFinalPrice)}</td>
                                            <td class="text-center receipt-table-cell-compact receipt-table-cell-qty">${item.quantity}</td>
                                            <td class="text-right receipt-table-cell-compact receipt-table-cell-total">${item.hidePrice ? 'N/A' : formatCurrency(item.totalPrice)}</td>
                                        </tr>
                                    `;
            }).join('')}
                                </tbody>
                            </table>
                        </div>
                        ` : ''}

                        ${isLastPage ? `
                        <!-- Summary Section -->
                        <div class="receipt-summary">
                            <div class="summary-row">
                                <span class="summary-label">Subtotal:</span>
                                <span class="summary-value">${formatCurrency(data.totals.subtotal)}</span>
                            </div>
                            <div class="summary-row">
                                <span class="summary-label">Discount:</span>
                                <span class="summary-value">-${formatCurrency(data.totals.discount)}</span>
                            </div>
                            <div class="summary-row">
                                <span class="summary-label">Tax:</span>
                                <span class="summary-value">${formatCurrency(data.totals.tax)}</span>
                            </div>
                            <div class="summary-row total-row">
                                <span class="summary-label">Total:</span>
                                <span class="summary-value">${formatCurrency(data.totals.total)}</span>
                            </div>
                        </div>

                        <!-- Payment Method -->
                        <div class="payment-section">
                            <h4>Payment Method</h4>
                            <div class="payment-method">${data.paymentMethod}</div>
                        </div>

                        ${data.notes ? `
                        <div class="notes-section">
                            <h4>Notes</h4>
                            <div class="notes-content">${data.notes}</div>
                        </div>
                        ` : ''}

                        <!-- Signature Section -->
                        <div class="signature-section">
                            <div class="signature-line">
                                <div class="signature-box">
                                    <div class="signature-label">Customer Signature</div>
                                    <div class="signature-space"></div>
                                    <div class="signature-date">Date: _______________</div>
                                </div>
                                <div class="signature-box">
                                    <div class="signature-label">Staff Signature</div>
                                    <div class="signature-space"></div>
                                    <div class="signature-date">Date: _______________</div>
                                </div>
                            </div>
                        </div>
                        ` : ''}
                    </div>
                </div>
            `;
        }).join('');

        return pagesHtml;
    }

    /**
     * Print Receipt
     */
    printReceipt() {
        // Get fresh receipt data instead of using cached HTML
        const receiptItems = window.ItemManager ? window.ItemManager.getReceiptItems() : [];
        if (receiptItems.length === 0) {
            if (typeof UIManager !== 'undefined') {
                const message = window.LanguageManager ?
                    window.LanguageManager.getText('error_no_items_to_print') :
                    'Please add items to the receipt first';
                UIManager.showMessage(message, 'warning');
            }
            return;
        }

        // Generate fresh receipt data for printing
        const customerName = document.getElementById('customerName')?.value.trim() || '';
        const customerPhone = document.getElementById('customerPhone')?.value.trim() || '';
        const customerEmail = document.getElementById('customerEmail')?.value.trim() || '';

        const receiptData = {
            customer: {
                name: customerName,
                phone: customerPhone,
                email: customerEmail,
                address: document.getElementById('customerAddress')?.value.trim() || ''
            },
            paymentMethod: this.defaultPaymentMethod,
            items: receiptItems.map(item => ({
                name: item.name,
                category: item.category,
                description: item.description,
                quantity: item.quantity,
                unitPrice: item.unitPrice,
                totalPrice: item.totalPrice,
                originalPrice: item.originalPrice,
                specialPrice: item.specialPrice,
                discountPercent: item.discountPercent,
                hidePrice: item.hidePrice
            })),
            totals: window.ItemManager ? window.ItemManager.calculateTotals() : this.calculateTotals(receiptItems),
            notes: document.getElementById('notes')?.value.trim() || '',
            receiptNumber: document.getElementById('receiptNumber')?.value || 'KMS-UltraVIP-0000001'
        };

        // Generate print HTML
        const receiptContent = this.generateReceiptHtmlForPrint(receiptData);

        // Open print window
        const printWindow = window.open('', '_blank', 'width=800,height=600');
        if (!printWindow) {
            alert('Please allow popups for this site to enable printing.');
            return;
        }

        const printHtml = `
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>Receipt - ${receiptData.receiptNumber}</title>
                <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
                <link href="css/receipt.css" rel="stylesheet">
                <link href="css/receipt-preview.css" rel="stylesheet">
                <link href="css/style.css" rel="stylesheet">
                <style>
                    @page {
                        size: letter;
                        margin: 0.5in;
                    }

                    body {
                        margin: 0;
                        padding: 0;
                        font-family: 'Courier New', monospace;
                        background: white;
                        -webkit-print-color-adjust: exact !important;
                        color-adjust: exact !important;
                        print-color-adjust: exact !important;
                    }

                    .receipt-page {
                        page-break-after: always;
                        margin: 0;
                        padding: 0;
                    }

                    .receipt-page:last-child {
                        page-break-after: auto;
                    }

                    /* Use the same styles as Receipt Preview */
                    .receipt-container {
                        position: relative;
                        max-width: 100%;
                        margin: 0 auto;
                        padding: 2rem 2.5rem;
                        font-family: 'Courier New', monospace;
                        font-size: 12pt;
                        line-height: 1.4;
                        background: white;
                        border-radius: 60px;
                        box-shadow:
                            0 0 0 3px #D4AF37,
                            0 0 0 6px white,
                            0 0 0 9px #B8860B,
                            0 0 0 12px white,
                            0 0 0 15px #DAA520,
                            0 8px 30px rgba(0, 0, 0, 0.2),
                            inset 0 0 0 2px #F5DEB3;
                        background-image:
                            radial-gradient(circle at 20px 20px, #F5DEB3 2px, transparent 2px),
                            radial-gradient(circle at 60px 60px, #F5DEB3 1px, transparent 1px);
                        background-size: 80px 80px, 40px 40px;
                        background-position: 0 0, 20px 20px;
                        -webkit-print-color-adjust: exact !important;
                        color-adjust: exact !important;
                        print-color-adjust: exact !important;
                    }

                    .no-print {
                        display: none !important;
                    }
                </style>
            </head>
            <body>
                ${receiptContent}
                <script>
                    window.onload = function() {
                        setTimeout(function() {
                            window.print();
                            window.close();
                        }, 1500);
                    };
                </script>
            </body>
            </html>
        `;

        printWindow.document.write(printHtml);
        printWindow.document.close();
    }

    /**
     * Calculate totals for items
     */
    calculateTotals(items) {
        let subtotal = 0;
        items.forEach(item => {
            subtotal += item.totalPrice || 0;
        });

        const discountAmount = parseFloat(document.getElementById('discountAmount')?.value) || 0;
        const taxRate = 0.1; // 10% tax rate
        const taxAmount = Math.max(0, (subtotal - discountAmount) * taxRate);
        const totalAmount = subtotal - discountAmount + taxAmount;

        return {
            subtotal: subtotal,
            discount: discountAmount,
            tax: taxAmount,
            total: Math.max(0, totalAmount)
        };
    }
}

// Create global instance
window.PrintManager = new PrintManager();
