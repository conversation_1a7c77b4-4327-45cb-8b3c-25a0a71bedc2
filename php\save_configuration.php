<?php
/**
 * 保存收據配置
 * KMS PC Receipt Maker
 */

require_once 'DatabaseMySQLi.php';
require_once 'Response.php';

header('Content-Type: application/json; charset=utf-8');

// 只允許POST請求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    Response::error('只允許POST請求', 405);
}

try {
    // 獲取JSON數據
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    if (!$data) {
        Response::error('無效的JSON數據');
    }
    
    // 驗證必需字段
    if (empty($data['name'])) {
        Response::error('配置名稱不能為空');
    }
    
    // 清理輸入數據
    $cleanData = Response::sanitizeInput($data);
    
    $db = new Database();
    
    // 檢查配置名稱是否已存在
    $existingSql = "SELECT id FROM receipt_configurations WHERE name = ?";
    $existing = $db->fetch($existingSql, [$cleanData['name']]);
    
    if ($existing) {
        // 更新現有配置
        $sql = "UPDATE receipt_configurations SET 
                customer_name = ?, customer_phone = ?, customer_email = ?, 
                customer_address = ?, payment_method = ?, items = ?, 
                discount_amount = ?, tax_rate = ?, notes = ?, updated_at = NOW()
                WHERE name = ?";
        
        $db->update($sql, [
            $cleanData['customerInfo']['name'] ?? null,
            $cleanData['customerInfo']['phone'] ?? null,
            $cleanData['customerInfo']['email'] ?? null,
            $cleanData['customerInfo']['address'] ?? null,
            $cleanData['paymentMethod'] ?? 'Cash',
            json_encode($cleanData['items'] ?? []),
            floatval($cleanData['discountAmount'] ?? 0),
            floatval($cleanData['taxRate'] ?? 0),
            $cleanData['notes'] ?? null,
            $cleanData['name']
        ]);
        
        Response::success(['id' => $existing['id']], '配置更新成功');
    } else {
        // 插入新配置
        $sql = "INSERT INTO receipt_configurations 
                (name, customer_name, customer_phone, customer_email, 
                 customer_address, payment_method, items, discount_amount, 
                 tax_rate, notes) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $configId = $db->insert($sql, [
            $cleanData['name'],
            $cleanData['customerInfo']['name'] ?? null,
            $cleanData['customerInfo']['phone'] ?? null,
            $cleanData['customerInfo']['email'] ?? null,
            $cleanData['customerInfo']['address'] ?? null,
            $cleanData['paymentMethod'] ?? 'Cash',
            json_encode($cleanData['items'] ?? []),
            floatval($cleanData['discountAmount'] ?? 0),
            floatval($cleanData['taxRate'] ?? 0),
            $cleanData['notes'] ?? null
        ]);
        
        Response::success(['id' => $configId], '配置保存成功');
    }
    
} catch (Exception $e) {
    error_log('Save configuration error: ' . $e->getMessage());
    Response::error('保存配置失敗: ' . $e->getMessage());
}
?>