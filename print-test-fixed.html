<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fixed Print Receipt Test - 修復多頁問題</title>
    <link href="css/receipt-preview.css" rel="stylesheet">
    <style>
        body {
            margin: 20px;
            font-family: Arial, sans-serif;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-info {
            background: #e8f5e8;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            border-left: 5px solid #28a745;
        }
        .print-button {
            background: #28a745;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .print-button:hover {
            background: #218838;
        }
        .fix-info {
            background: #fff3cd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #ffc107;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-info">
            <h1>✅ 修復完成 - Print Receipt Test</h1>
            <p><strong>問題修復：</strong>內容多了以後，Total 下面的內容都消失了</p>
            <p><strong>解決方案：</strong></p>
            <ul>
                <li>✅ 調整每頁項目數量從 22 個減少到 18 個</li>
                <li>✅ 智能分頁：如果最後一頁項目超過 15 個，自動創建摘要頁</li>
                <li>✅ 確保總計、付款方式、簽名始終顯示</li>
                <li>✅ 每頁都保持完整的四周邊框</li>
                <li>✅ 真正的 borderless 列印貼齊 letter 紙四邊</li>
            </ul>
        </div>

        <div class="fix-info">
            <h3>🔧 修復詳情</h3>
            <p><strong>原問題：</strong>只有在 <code>isLastPage</code> 時才顯示總計和簽名，導致內容多時被截斷</p>
            <p><strong>新邏輯：</strong>使用 <code>(isLastPage || isSummaryPage)</code> 確保重要內容始終顯示</p>
            <p><strong>分頁策略：</strong>如果最後一頁項目過多，自動創建專門的摘要頁面</p>
        </div>

        <button class="print-button" onclick="printLargeReceipt()">🖨️ 測試大量項目收據 (25項)</button>
        <button class="print-button" onclick="printMediumReceipt()">🖨️ 測試中等項目收據 (15項)</button>
        <button class="print-button" onclick="printSmallReceipt()">🖨️ 測試少量項目收據 (5項)</button>

        <!-- Large Receipt Test (25 items - should create summary page) -->
        <div id="largeReceiptTest" style="display: none;">
            <!-- Page 1 -->
            <div class="receipt-container">
                <div class="receipt-inner">
                    <div class="preview-receipt-header">
                        <div class="receipt-title">KelvinKMS</div>
                        <div class="receipt-company-info">
                            KelvinKMS.com<br>
                            626-464-8036<br>
                            <EMAIL>
                        </div>
                    </div>

                    <div class="preview-receipt-info">
                        <div>
                            <div class="receipt-number">Receipt Number: KMS-LARGE-TEST-001</div>
                        </div>
                    </div>

                    <div class="preview-customer-info">
                        <h6>Customer Information</h6>
                        <div class="customer-field">
                            <span class="customer-field-label">Name:</span>
                            <span class="customer-field-value">John Gaming Pro</span>
                        </div>
                        <div class="customer-field">
                            <span class="customer-field-label">Phone:</span>
                            <span class="customer-field-value">(*************</span>
                        </div>
                        <div class="customer-field">
                            <span class="customer-field-label">Email:</span>
                            <span class="customer-field-value"><EMAIL></span>
                        </div>
                    </div>

                    <div class="receipt-items">
                        <div class="items-header">
                            <h6>Items (Total: 25 items) - Page 1</h6>
                        </div>
                        <table class="receipt-table">
                            <thead>
                                <tr>
                                    <th class="text-center">#</th>
                                    <th class="text-left">Item</th>
                                    <th class="text-right">Price</th>
                                    <th class="text-center">Qty</th>
                                    <th class="text-right">Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr><td class="text-center item-number">1</td><td class="text-left"><div class="item-name">Gaming Laptop RTX 4090</div><div class="item-description">High-end gaming laptop</div></td><td class="text-right">$2,500</td><td class="text-center">1</td><td class="text-right">$2,500</td></tr>
                                <tr><td class="text-center item-number">2</td><td class="text-left"><div class="item-name">Mechanical Keyboard RGB</div><div class="item-description">Cherry MX switches</div></td><td class="text-right">$150</td><td class="text-center">1</td><td class="text-right">$150</td></tr>
                                <tr><td class="text-center item-number">3</td><td class="text-left"><div class="item-name">Gaming Mouse Pro</div><div class="item-description">25,000 DPI gaming mouse</div></td><td class="text-right">$80</td><td class="text-center">1</td><td class="text-right">$80</td></tr>
                                <tr><td class="text-center item-number">4</td><td class="text-left"><div class="item-name">Monitor 27" 4K 144Hz</div><div class="item-description">4K gaming monitor</div></td><td class="text-right">$400</td><td class="text-center">2</td><td class="text-right">$800</td></tr>
                                <tr><td class="text-center item-number">5</td><td class="text-left"><div class="item-name">SSD 2TB NVMe Gen4</div><div class="item-description">Ultra-fast NVMe SSD</div></td><td class="text-right">$250</td><td class="text-center">1</td><td class="text-right">$250</td></tr>
                                <tr><td class="text-center item-number">6</td><td class="text-left"><div class="item-name">RAM 64GB DDR5-6000</div><div class="item-description">High-speed DDR5 memory</div></td><td class="text-right">$400</td><td class="text-center">1</td><td class="text-right">$400</td></tr>
                                <tr><td class="text-center item-number">7</td><td class="text-left"><div class="item-name">CPU Cooler AIO 360mm</div><div class="item-description">360mm liquid cooler</div></td><td class="text-right">$200</td><td class="text-center">1</td><td class="text-right">$200</td></tr>
                                <tr><td class="text-center item-number">8</td><td class="text-left"><div class="item-name">Power Supply 1000W</div><div class="item-description">80+ Platinum PSU</div></td><td class="text-right">$200</td><td class="text-center">1</td><td class="text-right">$200</td></tr>
                                <tr><td class="text-center item-number">9</td><td class="text-left"><div class="item-name">Motherboard Z790 Extreme</div><div class="item-description">Premium Z790 motherboard</div></td><td class="text-right">$500</td><td class="text-center">1</td><td class="text-right">$500</td></tr>
                                <tr><td class="text-center item-number">10</td><td class="text-left"><div class="item-name">PC Case Full Tower</div><div class="item-description">Premium RGB case</div></td><td class="text-right">$200</td><td class="text-center">1</td><td class="text-right">$200</td></tr>
                                <tr><td class="text-center item-number">11</td><td class="text-left"><div class="item-name">Webcam 4K Pro</div><div class="item-description">Professional streaming cam</div></td><td class="text-right">$300</td><td class="text-center">1</td><td class="text-right">$300</td></tr>
                                <tr><td class="text-center item-number">12</td><td class="text-left"><div class="item-name">Headset Gaming Pro</div><div class="item-description">7.1 surround headset</div></td><td class="text-right">$150</td><td class="text-center">1</td><td class="text-right">$150</td></tr>
                                <tr><td class="text-center item-number">13</td><td class="text-left"><div class="item-name">Speakers 5.1 System</div><div class="item-description">Premium 5.1 speakers</div></td><td class="text-right">$300</td><td class="text-center">1</td><td class="text-right">$300</td></tr>
                                <tr><td class="text-center item-number">14</td><td class="text-left"><div class="item-name">USB Hub Thunderbolt</div><div class="item-description">Thunderbolt 4 hub</div></td><td class="text-right">$150</td><td class="text-center">1</td><td class="text-right">$150</td></tr>
                                <tr><td class="text-center item-number">15</td><td class="text-left"><div class="item-name">Cable Management Pro</div><div class="item-description">Premium cable kit</div></td><td class="text-right">$50</td><td class="text-center">1</td><td class="text-right">$50</td></tr>
                                <tr><td class="text-center item-number">16</td><td class="text-left"><div class="item-name">Thermal Paste Premium</div><div class="item-description">Liquid metal paste</div></td><td class="text-right">$25</td><td class="text-center">1</td><td class="text-right">$25</td></tr>
                                <tr><td class="text-center item-number">17</td><td class="text-left"><div class="item-name">WiFi Card AX6000</div><div class="item-description">WiFi 6E PCIe card</div></td><td class="text-right">$100</td><td class="text-center">1</td><td class="text-right">$100</td></tr>
                                <tr><td class="text-center item-number">18</td><td class="text-left"><div class="item-name">External SSD 4TB</div><div class="item-description">Portable SSD drive</div></td><td class="text-right">$400</td><td class="text-center">1</td><td class="text-right">$400</td></tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Page 2 - Summary Page -->
            <div class="receipt-container">
                <div class="receipt-inner">
                    <div class="preview-receipt-header">
                        <div class="receipt-title">KelvinKMS</div>
                        <div class="receipt-company-info">
                            KelvinKMS.com<br>
                            626-464-8036<br>
                            <EMAIL>
                        </div>
                    </div>

                    <div style="text-align: center; margin: 20px 0; font-weight: bold; color: #666;">
                        Receipt Summary - Page 2
                    </div>

                    <div class="receipt-items">
                        <table class="receipt-table">
                            <thead>
                                <tr>
                                    <th class="text-center">#</th>
                                    <th class="text-left">Item</th>
                                    <th class="text-right">Price</th>
                                    <th class="text-center">Qty</th>
                                    <th class="text-right">Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr><td class="text-center item-number">19</td><td class="text-left"><div class="item-name">Bluetooth Adapter Pro</div><div class="item-description">USB Bluetooth 5.2</div></td><td class="text-right">$40</td><td class="text-center">1</td><td class="text-right">$40</td></tr>
                                <tr><td class="text-center item-number">20</td><td class="text-left"><div class="item-name">Desk Pad XXL RGB</div><div class="item-description">RGB gaming desk pad</div></td><td class="text-right">$60</td><td class="text-center">1</td><td class="text-right">$60</td></tr>
                                <tr><td class="text-center item-number">21</td><td class="text-left"><div class="item-name">Monitor Arm Triple</div><div class="item-description">Triple monitor arm</div></td><td class="text-right">$150</td><td class="text-center">1</td><td class="text-right">$150</td></tr>
                                <tr><td class="text-center item-number">22</td><td class="text-left"><div class="item-name">LED Strip Addressable</div><div class="item-description">10m addressable RGB</div></td><td class="text-right">$80</td><td class="text-center">1</td><td class="text-right">$80</td></tr>
                                <tr><td class="text-center item-number">23</td><td class="text-left"><div class="item-name">UPS 1500VA</div><div class="item-description">Uninterruptible power</div></td><td class="text-right">$200</td><td class="text-center">1</td><td class="text-right">$200</td></tr>
                                <tr><td class="text-center item-number">24</td><td class="text-left"><div class="item-name">Network Switch 8-Port</div><div class="item-description">Gigabit switch</div></td><td class="text-right">$60</td><td class="text-center">1</td><td class="text-right">$60</td></tr>
                                <tr><td class="text-center item-number">25</td><td class="text-left"><div class="item-name">Cable Tester Pro</div><div class="item-description">Network cable tester</div></td><td class="text-right">$50</td><td class="text-center">1</td><td class="text-right">$50</td></tr>
                            </tbody>
                        </table>
                    </div>

                    <div class="receipt-totals" style="margin-top: 40px;">
                        <table class="totals-table">
                            <tr>
                                <td class="label">Subtotal:</td>
                                <td class="amount">$8,985</td>
                            </tr>
                            <tr>
                                <td class="label">Tax (8%):</td>
                                <td class="amount">$719</td>
                            </tr>
                            <tr class="total-row">
                                <td class="label">Total:</td>
                                <td class="amount">$9,704</td>
                            </tr>
                        </table>
                    </div>

                    <div class="payment-method" style="margin-top: 20px;">
                        <div class="payment-method-title" style="margin-bottom: 15px; font-size: 1.1rem; font-weight: bold; color: #333;">
                            Payment Method
                        </div>
                        <div class="payment-options">
                            <div class="payment-option-button">
                                <div class="payment-checkbox"></div>
                                <span class="payment-label">Cash</span>
                            </div>
                            <div class="payment-option-button">
                                <div class="payment-checkbox"></div>
                                <span class="payment-label">Venmo</span>
                            </div>
                            <div class="payment-option-button">
                                <div class="payment-checkbox"></div>
                                <span class="payment-label">Zelle</span>
                            </div>
                            <div class="payment-option-button">
                                <div class="payment-checkbox"></div>
                                <span class="payment-label">Square</span>
                            </div>
                            <div class="payment-option-button">
                                <div class="payment-checkbox"></div>
                                <span class="payment-label">Stripe</span>
                            </div>
                        </div>
                    </div>

                    <div class="receipt-notes" style="margin-top: 20px;">
                        <h6>Notes</h6>
                        <p>Premium gaming setup with professional streaming equipment. All items include 2-year warranty.</p>
                    </div>

                    <div class="signature-section" style="margin-top: 30px;">
                        <div class="signature-labels-row">
                            <div class="signature-label-item">
                                <span class="signature-label">Seller Signature:</span>
                            </div>
                            <div class="signature-label-item">
                                <span class="signature-label">Buyer Signature:</span>
                            </div>
                            <div class="signature-label-item">
                                <span class="signature-label">Signature Date:</span>
                            </div>
                        </div>
                        <div class="signature-lines-area">
                            <div class="signature-line-space"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Medium Receipt Test (15 items - single page with totals) -->
        <div id="mediumReceiptTest" style="display: none;">
            <div class="receipt-container">
                <div class="receipt-inner">
                    <div class="preview-receipt-header">
                        <div class="receipt-title">KelvinKMS</div>
                        <div class="receipt-company-info">
                            KelvinKMS.com<br>
                            626-464-8036<br>
                            <EMAIL>
                        </div>
                    </div>

                    <div class="preview-receipt-info">
                        <div>
                            <div class="receipt-number">Receipt Number: KMS-MEDIUM-TEST-002</div>
                        </div>
                    </div>

                    <div class="preview-customer-info">
                        <h6>Customer Information</h6>
                        <div class="customer-field">
                            <span class="customer-field-label">Name:</span>
                            <span class="customer-field-value">Jane Office Pro</span>
                        </div>
                        <div class="customer-field">
                            <span class="customer-field-label">Phone:</span>
                            <span class="customer-field-value">(*************</span>
                        </div>
                    </div>

                    <div class="receipt-items">
                        <div class="items-header">
                            <h6>Items (Total: 15 items)</h6>
                        </div>
                        <table class="receipt-table">
                            <thead>
                                <tr>
                                    <th class="text-center">#</th>
                                    <th class="text-left">Item</th>
                                    <th class="text-right">Price</th>
                                    <th class="text-center">Qty</th>
                                    <th class="text-right">Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr><td class="text-center item-number">1</td><td class="text-left"><div class="item-name">Business Laptop</div><div class="item-description">Professional laptop</div></td><td class="text-right">$1,200</td><td class="text-center">1</td><td class="text-right">$1,200</td></tr>
                                <tr><td class="text-center item-number">2</td><td class="text-left"><div class="item-name">Wireless Keyboard</div><div class="item-description">Ergonomic keyboard</div></td><td class="text-right">$80</td><td class="text-center">1</td><td class="text-right">$80</td></tr>
                                <tr><td class="text-center item-number">3</td><td class="text-left"><div class="item-name">Wireless Mouse</div><div class="item-description">Precision mouse</div></td><td class="text-right">$50</td><td class="text-center">1</td><td class="text-right">$50</td></tr>
                                <tr><td class="text-center item-number">4</td><td class="text-left"><div class="item-name">Monitor 24" FHD</div><div class="item-description">Full HD monitor</div></td><td class="text-right">$200</td><td class="text-center">2</td><td class="text-right">$400</td></tr>
                                <tr><td class="text-center item-number">5</td><td class="text-left"><div class="item-name">Docking Station</div><div class="item-description">USB-C dock</div></td><td class="text-right">$150</td><td class="text-center">1</td><td class="text-right">$150</td></tr>
                                <tr><td class="text-center item-number">6</td><td class="text-left"><div class="item-name">Webcam HD</div><div class="item-description">1080p webcam</div></td><td class="text-right">$80</td><td class="text-center">1</td><td class="text-right">$80</td></tr>
                                <tr><td class="text-center item-number">7</td><td class="text-left"><div class="item-name">Headset Business</div><div class="item-description">Noise-canceling headset</div></td><td class="text-right">$120</td><td class="text-center">1</td><td class="text-right">$120</td></tr>
                                <tr><td class="text-center item-number">8</td><td class="text-left"><div class="item-name">Printer All-in-One</div><div class="item-description">Print/scan/copy</div></td><td class="text-right">$200</td><td class="text-center">1</td><td class="text-right">$200</td></tr>
                                <tr><td class="text-center item-number">9</td><td class="text-left"><div class="item-name">External HDD 2TB</div><div class="item-description">Backup drive</div></td><td class="text-right">$80</td><td class="text-center">1</td><td class="text-right">$80</td></tr>
                                <tr><td class="text-center item-number">10</td><td class="text-left"><div class="item-name">USB Hub 7-Port</div><div class="item-description">USB 3.0 hub</div></td><td class="text-right">$40</td><td class="text-center">1</td><td class="text-right">$40</td></tr>
                                <tr><td class="text-center item-number">11</td><td class="text-left"><div class="item-name">Laptop Stand</div><div class="item-description">Adjustable stand</div></td><td class="text-right">$50</td><td class="text-center">1</td><td class="text-right">$50</td></tr>
                                <tr><td class="text-center item-number">12</td><td class="text-left"><div class="item-name">Cable Organizer</div><div class="item-description">Desk cable management</div></td><td class="text-right">$25</td><td class="text-center">1</td><td class="text-right">$25</td></tr>
                                <tr><td class="text-center item-number">13</td><td class="text-left"><div class="item-name">Desk Lamp LED</div><div class="item-description">Adjustable LED lamp</div></td><td class="text-right">$60</td><td class="text-center">1</td><td class="text-right">$60</td></tr>
                                <tr><td class="text-center item-number">14</td><td class="text-left"><div class="item-name">Surge Protector</div><div class="item-description">8-outlet protector</div></td><td class="text-right">$35</td><td class="text-center">1</td><td class="text-right">$35</td></tr>
                                <tr><td class="text-center item-number">15</td><td class="text-left"><div class="item-name">Cleaning Kit</div><div class="item-description">Screen cleaning kit</div></td><td class="text-right">$20</td><td class="text-center">1</td><td class="text-right">$20</td></tr>
                            </tbody>
                        </table>
                    </div>

                    <div class="receipt-totals" style="margin-top: 20px;">
                        <table class="totals-table">
                            <tr>
                                <td class="label">Subtotal:</td>
                                <td class="amount">$2,590</td>
                            </tr>
                            <tr>
                                <td class="label">Tax (8%):</td>
                                <td class="amount">$207</td>
                            </tr>
                            <tr class="total-row">
                                <td class="label">Total:</td>
                                <td class="amount">$2,797</td>
                            </tr>
                        </table>
                    </div>

                    <div class="payment-method" style="margin-top: 20px;">
                        <div class="payment-method-title" style="margin-bottom: 15px; font-size: 1.1rem; font-weight: bold; color: #333;">
                            Payment Method
                        </div>
                        <div class="payment-options">
                            <div class="payment-option-button">
                                <div class="payment-checkbox"></div>
                                <span class="payment-label">Cash</span>
                            </div>
                            <div class="payment-option-button">
                                <div class="payment-checkbox"></div>
                                <span class="payment-label">Card</span>
                            </div>
                        </div>
                    </div>

                    <div class="signature-section" style="margin-top: 30px;">
                        <div class="signature-labels-row">
                            <div class="signature-label-item">
                                <span class="signature-label">Seller Signature:</span>
                            </div>
                            <div class="signature-label-item">
                                <span class="signature-label">Buyer Signature:</span>
                            </div>
                            <div class="signature-label-item">
                                <span class="signature-label">Signature Date:</span>
                            </div>
                        </div>
                        <div class="signature-lines-area">
                            <div class="signature-line-space"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Small Receipt Test (5 items) -->
        <div id="smallReceiptTest" style="display: none;">
            <div class="receipt-container">
                <div class="receipt-inner">
                    <div class="preview-receipt-header">
                        <div class="receipt-title">KelvinKMS</div>
                        <div class="receipt-company-info">
                            KelvinKMS.com<br>
                            626-464-8036<br>
                            <EMAIL>
                        </div>
                    </div>

                    <div class="preview-receipt-info">
                        <div>
                            <div class="receipt-number">Receipt Number: KMS-SMALL-TEST-003</div>
                        </div>
                    </div>

                    <div class="preview-customer-info">
                        <h6>Customer Information</h6>
                        <div class="customer-field">
                            <span class="customer-field-label">Name:</span>
                            <span class="customer-field-value">Bob Simple</span>
                        </div>
                        <div class="customer-field">
                            <span class="customer-field-label">Phone:</span>
                            <span class="customer-field-value">(*************</span>
                        </div>
                    </div>

                    <div class="receipt-items">
                        <div class="items-header">
                            <h6>Items (Total: 5 items)</h6>
                        </div>
                        <table class="receipt-table">
                            <thead>
                                <tr>
                                    <th class="text-center">#</th>
                                    <th class="text-left">Item</th>
                                    <th class="text-right">Price</th>
                                    <th class="text-center">Qty</th>
                                    <th class="text-right">Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr><td class="text-center item-number">1</td><td class="text-left"><div class="item-name">Basic Laptop</div><div class="item-description">Entry-level laptop</div></td><td class="text-right">$500</td><td class="text-center">1</td><td class="text-right">$500</td></tr>
                                <tr><td class="text-center item-number">2</td><td class="text-left"><div class="item-name">USB Mouse</div><div class="item-description">Basic optical mouse</div></td><td class="text-right">$20</td><td class="text-center">1</td><td class="text-right">$20</td></tr>
                                <tr><td class="text-center item-number">3</td><td class="text-left"><div class="item-name">Laptop Bag</div><div class="item-description">Protective laptop bag</div></td><td class="text-right">$30</td><td class="text-center">1</td><td class="text-right">$30</td></tr>
                                <tr><td class="text-center item-number">4</td><td class="text-left"><div class="item-name">USB Cable</div><div class="item-description">USB-C to USB-A cable</div></td><td class="text-right">$15</td><td class="text-center">2</td><td class="text-right">$30</td></tr>
                                <tr><td class="text-center item-number">5</td><td class="text-left"><div class="item-name">Screen Protector</div><div class="item-description">Anti-glare screen protector</div></td><td class="text-right">$10</td><td class="text-center">1</td><td class="text-right">$10</td></tr>
                            </tbody>
                        </table>
                    </div>

                    <div class="receipt-totals" style="margin-top: 20px;">
                        <table class="totals-table">
                            <tr>
                                <td class="label">Subtotal:</td>
                                <td class="amount">$590</td>
                            </tr>
                            <tr>
                                <td class="label">Tax (8%):</td>
                                <td class="amount">$47</td>
                            </tr>
                            <tr class="total-row">
                                <td class="label">Total:</td>
                                <td class="amount">$637</td>
                            </tr>
                        </table>
                    </div>

                    <div class="payment-method" style="margin-top: 20px;">
                        <div class="payment-method-title" style="margin-bottom: 15px; font-size: 1.1rem; font-weight: bold; color: #333;">
                            Payment Method
                        </div>
                        <div class="payment-options">
                            <div class="payment-option-button">
                                <div class="payment-checkbox"></div>
                                <span class="payment-label">Cash</span>
                            </div>
                            <div class="payment-option-button">
                                <div class="payment-checkbox"></div>
                                <span class="payment-label">Card</span>
                            </div>
                        </div>
                    </div>

                    <div class="signature-section" style="margin-top: 30px;">
                        <div class="signature-labels-row">
                            <div class="signature-label-item">
                                <span class="signature-label">Seller Signature:</span>
                            </div>
                            <div class="signature-label-item">
                                <span class="signature-label">Buyer Signature:</span>
                            </div>
                            <div class="signature-label-item">
                                <span class="signature-label">Signature Date:</span>
                            </div>
                        </div>
                        <div class="signature-lines-area">
                            <div class="signature-line-space"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function printLargeReceipt() {
            const content = document.getElementById('largeReceiptTest').innerHTML;
            openPrintWindow(content, 'Large Receipt Test - 25 Items');
        }

        function printMediumReceipt() {
            const content = document.getElementById('mediumReceiptTest').innerHTML;
            openPrintWindow(content, 'Medium Receipt Test - 15 Items');
        }

        function printSmallReceipt() {
            const content = document.getElementById('smallReceiptTest').innerHTML;
            openPrintWindow(content, 'Small Receipt Test - 5 Items');
        }

        function openPrintWindow(content, title) {
            const printWindow = window.open('', '_blank');
            const printHtml = `
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>${title}</title>
                    <link href="css/receipt-preview.css" rel="stylesheet">
                    <style>
                        @page {
                            size: letter;
                            margin: 0 !important;
                        }
                        html, body {
                            margin: 0 !important;
                            padding: 0 !important;
                            background: white !important;
                            -webkit-print-color-adjust: exact !important;
                            color-adjust: exact !important;
                            print-color-adjust: exact !important;
                        }
                    </style>
                </head>
                <body>
                    ${content}
                    <script>
                        window.onload = function() {
                            setTimeout(function() {
                                window.print();
                                window.close();
                            }, 1000);
                        };
                    </script>
                </body>
                </html>
            `;
            printWindow.document.write(printHtml);
            printWindow.document.close();
        }
    </script>
</body>
</html>