<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Print Receipt Test - Borderless with Beautiful Borders</title>
    <link href="css/receipt-preview.css" rel="stylesheet">
    <style>
        /* Test styles for preview */
        body {
            margin: 20px;
            font-family: Arial, sans-serif;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .print-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .print-button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-info">
            <h1>🖨️ Print Receipt Test - 完美無邊框列印</h1>
            <p><strong>測試目標：</strong>確保收據無論生成幾頁，都能保持四周的優美邊框，並且貼齊頁面 borderless</p>
            <p><strong>優化內容：</strong></p>
            <ul>
                <li>✅ 真正的 borderless 列印 (margin: 0)</li>
                <li>✅ 每頁都有完整的金色多層邊框</li>
                <li>✅ 完美的圓角設計 (60px border-radius)</li>
                <li>✅ 多頁收據每頁都有一致的邊框</li>
                <li>✅ 優化的內容間距和字體大小</li>
                <li>✅ 增強的顏色保留設定</li>
            </ul>
            <button class="print-button" onclick="printSinglePage()">🖨️ 列印單頁測試</button>
            <button class="print-button" onclick="printMultiPage()">🖨️ 列印多頁測試</button>
        </div>

        <!-- Single Page Test -->
        <div id="singlePageTest" style="display: none;">
            <div class="receipt-container">
                <div class="receipt-inner">
                    <div class="preview-receipt-header">
                        <div class="receipt-title">KelvinKMS</div>
                        <div class="receipt-company-info">
                            KelvinKMS.com<br>
                            626-464-8036<br>
                            <EMAIL>
                        </div>
                    </div>

                    <div class="preview-receipt-info">
                        <div>
                            <div class="receipt-number">Receipt Number: KMS-UltraVIP-0000001</div>
                        </div>
                    </div>

                    <div class="preview-customer-info">
                        <h6>Customer Information</h6>
                        <div class="customer-field">
                            <span class="customer-field-label">Name:</span>
                            <span class="customer-field-value">John Doe</span>
                        </div>
                        <div class="customer-field">
                            <span class="customer-field-label">Phone:</span>
                            <span class="customer-field-value">(*************</span>
                        </div>
                        <div class="customer-field">
                            <span class="customer-field-label">Email:</span>
                            <span class="customer-field-value"><EMAIL></span>
                        </div>
                    </div>

                    <div class="receipt-items">
                        <div class="items-header">
                            <h6>Items (Total: 3 items)</h6>
                        </div>
                        <table class="receipt-table">
                            <thead>
                                <tr>
                                    <th class="text-center">#</th>
                                    <th class="text-left">Item</th>
                                    <th class="text-right">Price</th>
                                    <th class="text-center">Qty</th>
                                    <th class="text-right">Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="text-center item-number">1</td>
                                    <td class="text-left">
                                        <div class="item-name">Gaming Laptop</div>
                                        <div class="item-description">High-performance gaming laptop</div>
                                    </td>
                                    <td class="text-right">$1,200</td>
                                    <td class="text-center">1</td>
                                    <td class="text-right">$1,200</td>
                                </tr>
                                <tr>
                                    <td class="text-center item-number">2</td>
                                    <td class="text-left">
                                        <div class="item-name">Wireless Mouse</div>
                                        <div class="item-description">Ergonomic wireless mouse</div>
                                    </td>
                                    <td class="text-right">$50</td>
                                    <td class="text-center">2</td>
                                    <td class="text-right">$100</td>
                                </tr>
                                <tr>
                                    <td class="text-center item-number">3</td>
                                    <td class="text-left">
                                        <div class="item-name">Mechanical Keyboard</div>
                                        <div class="item-description">RGB mechanical keyboard</div>
                                    </td>
                                    <td class="text-right">$150</td>
                                    <td class="text-center">1</td>
                                    <td class="text-right">$150</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <div class="receipt-totals">
                        <table class="totals-table">
                            <tr>
                                <td class="label">Subtotal:</td>
                                <td class="amount">$1,450</td>
                            </tr>
                            <tr>
                                <td class="label">Tax:</td>
                                <td class="amount">$116</td>
                            </tr>
                            <tr class="total-row">
                                <td class="label">Total:</td>
                                <td class="amount">$1,566</td>
                            </tr>
                        </table>
                    </div>

                    <div class="payment-method">
                        <div class="payment-options">
                            <div class="payment-option-button">
                                <div class="payment-checkbox"></div>
                                <span class="payment-label">Cash</span>
                            </div>
                            <div class="payment-option-button">
                                <div class="payment-checkbox"></div>
                                <span class="payment-label">Card</span>
                            </div>
                        </div>
                    </div>

                    <div class="signature-section">
                        <div class="signature-labels-row">
                            <div class="signature-label-item">
                                <span class="signature-label">Seller Signature:</span>
                            </div>
                            <div class="signature-label-item">
                                <span class="signature-label">Buyer Signature:</span>
                            </div>
                        </div>
                        <div class="signature-lines-area">
                            <div class="signature-line-space"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Multi Page Test -->
        <div id="multiPageTest" style="display: none;">
            <!-- Page 1 -->
            <div class="receipt-container">
                <div class="receipt-inner">
                    <div class="preview-receipt-header">
                        <div class="receipt-title">KelvinKMS</div>
                        <div class="receipt-company-info">
                            KelvinKMS.com<br>
                            626-464-8036<br>
                            <EMAIL>
                        </div>
                    </div>

                    <div class="preview-receipt-info">
                        <div>
                            <div class="receipt-number">Receipt Number: KMS-UltraVIP-0000002</div>
                        </div>
                    </div>

                    <div class="preview-customer-info">
                        <h6>Customer Information</h6>
                        <div class="customer-field">
                            <span class="customer-field-label">Name:</span>
                            <span class="customer-field-value">Jane Smith</span>
                        </div>
                        <div class="customer-field">
                            <span class="customer-field-label">Phone:</span>
                            <span class="customer-field-value">(*************</span>
                        </div>
                    </div>

                    <div class="receipt-items">
                        <div class="items-header">
                            <h6>Items (Total: 25 items) - Page 1</h6>
                        </div>
                        <table class="receipt-table">
                            <thead>
                                <tr>
                                    <th class="text-center">#</th>
                                    <th class="text-left">Item</th>
                                    <th class="text-right">Price</th>
                                    <th class="text-center">Qty</th>
                                    <th class="text-right">Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Generate 18 items for page 1 -->
                                <tr><td class="text-center item-number">1</td><td class="text-left"><div class="item-name">Gaming Laptop RTX 4090</div><div class="item-description">High-end gaming laptop with RTX 4090</div></td><td class="text-right">$2,500</td><td class="text-center">1</td><td class="text-right">$2,500</td></tr>
                                <tr><td class="text-center item-number">2</td><td class="text-left"><div class="item-name">Mechanical Keyboard</div><div class="item-description">RGB mechanical keyboard</div></td><td class="text-right">$150</td><td class="text-center">1</td><td class="text-right">$150</td></tr>
                                <tr><td class="text-center item-number">3</td><td class="text-left"><div class="item-name">Gaming Mouse</div><div class="item-description">High DPI gaming mouse</div></td><td class="text-right">$80</td><td class="text-center">1</td><td class="text-right">$80</td></tr>
                                <tr><td class="text-center item-number">4</td><td class="text-left"><div class="item-name">Monitor 27" 4K</div><div class="item-description">4K gaming monitor</div></td><td class="text-right">$400</td><td class="text-center">1</td><td class="text-right">$400</td></tr>
                                <tr><td class="text-center item-number">5</td><td class="text-left"><div class="item-name">SSD 1TB NVMe</div><div class="item-description">High-speed NVMe SSD</div></td><td class="text-right">$120</td><td class="text-center">1</td><td class="text-right">$120</td></tr>
                                <tr><td class="text-center item-number">6</td><td class="text-left"><div class="item-name">RAM 32GB DDR5</div><div class="item-description">High-speed DDR5 memory</div></td><td class="text-right">$200</td><td class="text-center">1</td><td class="text-right">$200</td></tr>
                                <tr><td class="text-center item-number">7</td><td class="text-left"><div class="item-name">CPU Cooler AIO</div><div class="item-description">240mm AIO liquid cooler</div></td><td class="text-right">$130</td><td class="text-center">1</td><td class="text-right">$130</td></tr>
                                <tr><td class="text-center item-number">8</td><td class="text-left"><div class="item-name">Power Supply 850W</div><div class="item-description">80+ Gold modular PSU</div></td><td class="text-right">$150</td><td class="text-center">1</td><td class="text-right">$150</td></tr>
                                <tr><td class="text-center item-number">9</td><td class="text-left"><div class="item-name">Motherboard Z790</div><div class="item-description">High-end Z790 motherboard</div></td><td class="text-right">$300</td><td class="text-center">1</td><td class="text-right">$300</td></tr>
                                <tr><td class="text-center item-number">10</td><td class="text-left"><div class="item-name">PC Case RGB</div><div class="item-description">Tempered glass RGB case</div></td><td class="text-right">$120</td><td class="text-center">1</td><td class="text-right">$120</td></tr>
                                <tr><td class="text-center item-number">11</td><td class="text-left"><div class="item-name">Webcam 4K</div><div class="item-description">4K streaming webcam</div></td><td class="text-right">$200</td><td class="text-center">1</td><td class="text-right">$200</td></tr>
                                <tr><td class="text-center item-number">12</td><td class="text-left"><div class="item-name">Headset Gaming</div><div class="item-description">7.1 surround gaming headset</div></td><td class="text-right">$100</td><td class="text-center">1</td><td class="text-right">$100</td></tr>
                                <tr><td class="text-center item-number">13</td><td class="text-left"><div class="item-name">Speakers 2.1</div><div class="item-description">High-quality 2.1 speakers</div></td><td class="text-right">$80</td><td class="text-center">1</td><td class="text-right">$80</td></tr>
                                <tr><td class="text-center item-number">14</td><td class="text-left"><div class="item-name">USB Hub 10-Port</div><div class="item-description">10-port USB 3.0 hub</div></td><td class="text-right">$50</td><td class="text-center">1</td><td class="text-right">$50</td></tr>
                                <tr><td class="text-center item-number">15</td><td class="text-left"><div class="item-name">Cable Management Kit</div><div class="item-description">Complete cable management</div></td><td class="text-right">$30</td><td class="text-center">1</td><td class="text-right">$30</td></tr>
                                <tr><td class="text-center item-number">16</td><td class="text-left"><div class="item-name">Thermal Paste</div><div class="item-description">High-performance thermal paste</div></td><td class="text-right">$15</td><td class="text-center">1</td><td class="text-right">$15</td></tr>
                                <tr><td class="text-center item-number">17</td><td class="text-left"><div class="item-name">WiFi Card AX</div><div class="item-description">WiFi 6E PCIe card</div></td><td class="text-right">$60</td><td class="text-center">1</td><td class="text-right">$60</td></tr>
                                <tr><td class="text-center item-number">18</td><td class="text-left"><div class="item-name">External HDD 4TB</div><div class="item-description">4TB external backup drive</div></td><td class="text-right">$100</td><td class="text-center">1</td><td class="text-right">$100</td></tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Page 2 -->
            <div class="receipt-container">
                <div class="receipt-inner">
                    <div class="preview-receipt-header">
                        <div class="receipt-title">KelvinKMS</div>
                        <div class="receipt-company-info">
                            KelvinKMS.com<br>
                            626-464-8036<br>
                            <EMAIL>
                        </div>
                    </div>

                    <div style="text-align: center; margin: 20px 0; font-weight: bold; color: #666;">
                        Items (continued) - Page 2
                    </div>

                    <div class="receipt-items">
                        <table class="receipt-table">
                            <thead>
                                <tr>
                                    <th class="text-center">#</th>
                                    <th class="text-left">Item</th>
                                    <th class="text-right">Price</th>
                                    <th class="text-center">Qty</th>
                                    <th class="text-right">Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Generate remaining items for page 2 -->
                                <tr><td class="text-center item-number">19</td><td class="text-left"><div class="item-name">Bluetooth Adapter</div><div class="item-description">USB Bluetooth 5.0 adapter</div></td><td class="text-right">$25</td><td class="text-center">1</td><td class="text-right">$25</td></tr>
                                <tr><td class="text-center item-number">20</td><td class="text-left"><div class="item-name">Desk Pad XL</div><div class="item-description">Extra large gaming desk pad</div></td><td class="text-right">$40</td><td class="text-center">1</td><td class="text-right">$40</td></tr>
                                <tr><td class="text-center item-number">21</td><td class="text-left"><div class="item-name">Monitor Arm Dual</div><div class="item-description">Dual monitor mounting arm</div></td><td class="text-right">$80</td><td class="text-center">1</td><td class="text-right">$80</td></tr>
                                <tr><td class="text-center item-number">22</td><td class="text-left"><div class="item-name">LED Strip RGB</div><div class="item-description">5m RGB LED strip</div></td><td class="text-right">$35</td><td class="text-center">1</td><td class="text-right">$35</td></tr>
                                <tr><td class="text-center item-number">23</td><td class="text-left"><div class="item-name">Surge Protector</div><div class="item-description">12-outlet surge protector</div></td><td class="text-right">$45</td><td class="text-center">1</td><td class="text-right">$45</td></tr>
                            </tbody>
                        </table>
                    </div>

                    <div class="receipt-totals">
                        <table class="totals-table">
                            <tr>
                                <td class="label">Subtotal:</td>
                                <td class="amount">$5,250</td>
                            </tr>
                            <tr>
                                <td class="label">Tax:</td>
                                <td class="amount">$420</td>
                            </tr>
                            <tr class="total-row">
                                <td class="label">Total:</td>
                                <td class="amount">$5,785</td>
                            </tr>
                        </table>
                    </div>

                    <div class="payment-method">
                        <div class="payment-options">
                            <div class="payment-option-button">
                                <div class="payment-checkbox"></div>
                                <span class="payment-label">Cash</span>
                            </div>
                            <div class="payment-option-button">
                                <div class="payment-checkbox"></div>
                                <span class="payment-label">Card</span>
                            </div>
                        </div>
                    </div>

                    <div class="signature-section">
                        <div class="signature-labels-row">
                            <div class="signature-label-item">
                                <span class="signature-label">Seller Signature:</span>
                            </div>
                            <div class="signature-label-item">
                                <span class="signature-label">Buyer Signature:</span>
                            </div>
                        </div>
                        <div class="signature-lines-area">
                            <div class="signature-line-space"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function printSinglePage() {
            const content = document.getElementById('singlePageTest').innerHTML;
            openPrintWindow(content, 'Single Page Receipt Test');
        }

        function printMultiPage() {
            const content = document.getElementById('multiPageTest').innerHTML;
            openPrintWindow(content, 'Multi Page Receipt Test');
        }

        function openPrintWindow(content, title) {
            const printWindow = window.open('', '_blank');
            const printHtml = `
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>${title}</title>
                    <link href="css/receipt-preview.css" rel="stylesheet">
                    <style>
                        @page {
                            size: letter;
                            margin: 0 !important;
                        }
                        html, body {
                            margin: 0 !important;
                            padding: 0 !important;
                            background: white !important;
                            -webkit-print-color-adjust: exact !important;
                            color-adjust: exact !important;
                            print-color-adjust: exact !important;
                        }
                    </style>
                </head>
                <body>
                    ${content}
                    <script>
                        window.onload = function() {
                            setTimeout(function() {
                                window.print();
                                window.close();
                            }, 1000);
                        };
                    </script>
                </body>
                </html>
            `;
            printWindow.document.write(printHtml);
            printWindow.document.close();
        }
    </script>
</body>
</html>