-- KMS PC Receipt Maker - Payment Method Migration
-- 將舊的 payment method 值遷移到新的格式

USE kms_receipt_maker;

-- 首先修改 receipts 表的 payment_method 欄位類型
ALTER TABLE receipts MODIFY COLUMN payment_method VARCHAR(50) DEFAULT 'Cash';

-- 更新現有的 payment method 數據
UPDATE receipts SET payment_method = 'Cash' WHERE payment_method = 'cash';
UPDATE receipts SET payment_method = 'Venmo' WHERE payment_method = 'card';
UPDATE receipts SET payment_method = 'Zelle' WHERE payment_method = 'transfer';
UPDATE receipts SET payment_method = 'Square' WHERE payment_method = 'other';

-- 更新 receipt_configurations 表的數據
UPDATE receipt_configurations SET payment_method = 'Cash' WHERE payment_method = 'cash';
UPDATE receipt_configurations SET payment_method = 'Venmo' WHERE payment_method = 'card';
UPDATE receipt_configurations SET payment_method = 'Zelle' WHERE payment_method = 'transfer';
UPDATE receipt_configurations SET payment_method = 'Square' WHERE payment_method = 'other';

-- 最後將 payment_method 欄位改為 ENUM 類型（可選，如果需要嚴格控制）
-- ALTER TABLE receipts MODIFY COLUMN payment_method ENUM('Cash', 'Venmo', 'Zelle', 'Square', 'Stripe') DEFAULT 'Cash';
