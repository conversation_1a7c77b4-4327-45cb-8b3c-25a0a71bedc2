/* KMS PC Receipt Maker - Optimized Stylesheet (px units) */

/* --- Base Receipt Container --- */
.receipt {
    background: #fff;
    padding: 32px;
    border-radius: 60px; /* Extra round corners */
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    max-width: 800px;
    margin: 0 auto;
    font-family: Arial, Helvetica, sans-serif;
    font-size: 20px;
    line-height: 1.4;
}

/* --- Screen Preview Styles --- */
@media screen {
    /* Force consistent font for preview */
    .receipt * {
        font-family: Arial, Helvetica, sans-serif !important;
        font-size: 20px !important;
    }
    .receipt-preview {
        overflow: auto; /* Allow scrolling for letter-sized preview */
    }
    .receipt-preview .receipt {
        width: 8.5in;
        min-height: 11in;
        max-width: none;
        padding: 0.5in; /* Simulate printer margins */
        margin: 0 auto;
    }
}

/* --- Enhanced Print Styles for Perfect Borderless Printing --- */
@media print {
    @page {
        size: letter;
        margin: 0 !important; /* True borderless printing */
    }
    
    html, body {
        margin: 0 !important;
        padding: 0 !important;
        background: #fff !important;
        width: 8.5in !important;
        height: 11in !important;
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
    }
    
    .receipt {
        /* Perfect borderless receipt with beautiful borders */
        box-shadow: none !important;
        overflow: hidden !important;
        padding: 0.3in !important; /* Optimal content padding */
        margin: 0 !important;
        max-width: none !important;
        width: 8.5in !important;
        height: 11in !important;
        border: 4px solid #D4AF37 !important; /* Prominent gold border */
        border-radius: 60px !important; /* Beautiful rounded corners */
        box-sizing: border-box !important;
        page-break-inside: avoid;
        page-break-after: always;
        position: relative !important;
        background: 
            radial-gradient(circle at 20px 20px, #F5DEB3 2px, transparent 2px),
            radial-gradient(circle at 60px 60px, #F5DEB3 1px, transparent 1px),
            white !important;
        background-size: 80px 80px, 40px 40px, auto !important;
    }

    /* Remove page break from last receipt */
    .receipt:last-child {
        page-break-after: auto !important;
    }

    /* Enhanced decorative border rings */
    .receipt::before {
        content: '' !important;
        position: absolute !important;
        top: 0.08in !important;
        left: 0.08in !important;
        right: 0.08in !important;
        bottom: 0.08in !important;
        z-index: 1 !important;
        border: 3px solid #D4AF37 !important;
        border-radius: 54px !important;
        box-shadow:
            0 0 0 4px white,
            0 0 0 7px #B8860B,
            0 0 0 10px white,
            0 0 0 13px #DAA520,
            inset 0 0 0 3px #F5DEB3 !important;
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
    }

    .receipt::after {
        content: '' !important;
        position: absolute !important;
        top: 0.15in !important;
        left: 0.15in !important;
        right: 0.15in !important;
        bottom: 0.15in !important;
        z-index: 1 !important;
        border: 2px solid #B8860B !important;
        border-radius: 48px !important;
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
    }

    /* Content positioning above decorative borders */
    .receipt > * {
        position: relative !important;
        z-index: 2 !important;
    }
    
    /* Ensure consistent font for printing */
    .receipt, .receipt * {
        font-family: Arial, Helvetica, sans-serif !important;
        font-size: 20px !important;
    }
    
    /* Force background colors to print with enhanced color preservation */
    .customer-info,
    .payment-info,
    .receipt-notes,
    .items-table th {
        background-color: #f8f9fa !important;
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
    }
    
    /* Hide non-printable elements */
    .no-print, .btn, .navbar, .modal-header, .modal-footer, .resize-handle {
        display: none !important;
    }
    
    .draggable-logo {
        border: none !important;
        cursor: default !important;
    }
    
    /* Enhanced table printing with border preservation */
    .items-table {
        page-break-inside: auto !important;
        width: 100% !important;
        border-collapse: collapse !important;
    }

    .items-table thead {
        display: table-header-group !important;
    }

    .items-table tbody tr {
        page-break-inside: avoid !important;
        break-inside: avoid !important;
    }

    /* Keep important sections together */
    .receipt-totals,
    .customer-info,
    .payment-info,
    .receipt-notes,
    .signature-section {
        page-break-inside: avoid !important;
    }
}

/* --- Receipt Sections --- */
.receipt-header {
    text-align: center;
    border-bottom: 2px solid #333;
    padding-bottom: 16px;
    margin-bottom: 24px;
}
.receipt-header .company-name {
    font-size: 29px;
    font-weight: bold;
    color: #333;
    margin-bottom: 8px;
}
.receipt-header .company-info {
    color: #666;
    line-height: 1.3;
}
.receipt-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}
.receipt-info .receipt-number {
    white-space: nowrap;
    font-weight: bold;
    color: #0d6efd;
}
.receipt-info .receipt-date {
    color: #666;
}
.receipt-info .right {
    text-align: right;
}

/* Customer, Payment, and Notes Sections */
.customer-info,
.payment-info,
.receipt-notes {
    margin-top: 24px;
    padding: 16px;
    border-radius: 4px;
    background-color: #f8f9fa;
}
.customer-info h6 {
    margin-bottom: 12px;
    color: #333;
    font-weight: bold;
    border-bottom: 1px solid #ddd;
    padding-bottom: 4px;
}
.customer-info .info-row:first-of-type {
    display: flex;
    gap: 8px;
    align-items: baseline;
    white-space: nowrap;
}
.customer-info .info-label {
    font-weight: bold;
    width: 80px;
    color: #555;
}
.payment-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.payment-method, .payment-status {
    font-weight: bold;
}
.payment-status {
    color: #198754;
}
.receipt-notes {
    background-color: #fff3cd;
    border-left: 4px solid #ffc107;
}
.receipt-notes h6, .receipt-notes p {
    color: #856404;
    margin: 0;
}
.receipt-notes h6 {
    margin-bottom: 8px;
}

/* --- Items & Totals Tables --- */
.items-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 16px;
}
.items-table th,
.items-table td {
    padding: 12px 8px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}
.items-table th {
    background-color: #f8f9fa;
    font-weight: bold;
    color: #333;
    border-bottom: 2px solid #333;
}
.items-table .text-right { text-align: right; }
.items-table .text-center { text-align: center; }
.items-table tbody tr:hover { background-color: #f8f9fa; }

.receipt-totals {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 2px solid #333;
}
.totals-table {
    width: 100%;
    max-width: 300px;
    margin-left: auto;
}
.totals-table td {
    padding: 8px 16px;
    border: none;
}
.totals-table .label {
    font-weight: bold;
    text-align: right;
    color: #555;
}
.totals-table .amount {
    text-align: right;
    font-weight: bold;
}
.totals-table .total-row {
    border-top: 1px solid #333;
    font-size: 18px;
}
.totals-table .total-row .label,
.totals-table .total-row .amount {
    color: #333;
}

/* --- Footer & Signature --- */
.receipt-footer {
    margin-top: 32px;
    padding-top: 16px;
    border-top: 1px solid #ddd;
    text-align: center;
    color: #666;
    font-size: 14px;
}
.receipt-footer .thank-you {
    font-weight: bold;
    color: #333;
    margin-bottom: 24px;
}
.signature-section {
    margin-top: 32px;
    padding-top: 16px;
    border-top: 1px solid #ddd;
    text-align: left;
}
.signature-row {
    display: flex;
    justify-content: flex-start;
    margin-bottom: 24px;
    gap: 32px;
}
.signature-field {
    flex: 1;
}
.signature-line {
    width: 150px;
    border-bottom: 1px solid #333;
    margin-bottom: 4px;
}
.signature-label {
    font-size: 14px;
    color: #666;
    font-weight: bold;
    margin-bottom: 8px;
}

/* --- Interactive Elements --- */
/* Logo Drag & Resize */
.draggable-logo {
    cursor: move;
    border: 2px dashed transparent;
    transition: border-color 0.3s ease;
    user-select: none;
}
.draggable-logo:hover {
    border-color: #007bff;
}
.resize-handle {
    position: absolute;
    width: 12px;
    height: 12px;
    background: #007bff;
    border: 2px solid white;
    border-radius: 50%;
    cursor: se-resize;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    opacity: 0;
    transition: opacity 0.3s ease;
}
.draggable-logo:hover + .resize-handle,
.resize-handle:hover {
    opacity: 1;
}

/* --- Items Management Section --- */
.receipt-items-container {
    max-height: 600px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 8px;
    counter-reset: item-counter; /* Initialize item counter */
}
.receipt-item {
    border: 1px solid #ffffffba;
    border-radius: 12px;
    margin-bottom: 3px;
    background: #ffceceba;
    transition: all 0.2s ease;
    position: relative;
}
.receipt-item:hover {
    border-color: #ffffff;
    box-shadow: 0 2px 8px rgba(255, 255, 255, 0.548);
}
.receipt-item:last-child { margin-bottom: 0; }
.receipt-item .row { align-items: center; margin: 0; }
.receipt-item .col-md-4, .receipt-item .col-md-2 { padding: 4px 8px; }

/* Item Info & Price */
.item-info h6 {
    font-size: 15px;
    font-weight: 600;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.item-category, .item-description {
    font-size: 13px;
    color: #6c757d;
    margin: 0 0 4px;
}
.price-display {
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    gap: 6px;
}
.original-price { font-size: 13px; }
.special-price { font-size: 14px; font-weight: bold; }
.discount-badge { font-size: 11px; padding: 3px 6px; }
.item-total-price {
    font-weight: bold;
    font-size: 15px;
    color: #198754;
}

/* Item Form Controls & Actions */
.receipt-item .form-label {
    font-size: 13px;
    font-weight: 600;
    margin-bottom: 4px;
    color: #495057;
}
.receipt-item .form-control-sm {
    font-size: 14px;
    padding: 4px 8px;
    height: auto;
    width: 100px;
}
.item-actions {
    display: flex;
    flex-direction: row;
    gap: 6px;
    justify-content: center;
}
.item-actions .btn-xs {
    padding: 6px 8px;
    font-size: 13px;
    border-radius: 6px;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.item-actions .btn-sm {
    padding: 8px 12px;
    font-size: 12px;
    border-radius: 8px;
    min-width: 80px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
}

/* --- Drag and Drop (Sortable.js) --- */
.receipt-item.draggable {
    cursor: move;
    position: relative;
    padding-left: 40px; /* Make room for drag handle */
}
.drag-handle {
    position: absolute;
    left: 8px;
    top: 50%;
    transform: translateY(-50%);
    cursor: grab;
    color: #00e5ff;
    font-size: 20px;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
    z-index: 10;
}
.drag-handle:hover {
    color: #0d6efd;
    background-color: rgba(13, 110, 253, 0.1);
}

/* Ensure item content is properly positioned when draggable */
.receipt-item.draggable .row {
    margin-left: 0;
    position: relative;
}
.receipt-item.draggable .item-info {
    margin-left: 0;
    padding-left: 0;
}
/* Ghost: Placeholder style for where the item will be dropped */
.receipt-item.sortable-ghost {
    opacity: 0.4;
    background-color: #f8f9fa;
    border-style: dashed;
}
/* Chosen: Style of the item being dragged */
.receipt-item.sortable-chosen {
    opacity: 0.8;
    cursor: grabbing;
    transform: rotate(2deg);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

/* Item numbering for UX */
.receipt-item::before {
    content: counter(item-counter);
    counter-increment: item-counter;
    position: absolute;
    top: 3px;
    right: 3px;
    background: #ffffff00;
    color: #ffffff;
    font-size: 8px;
    padding: 3px 6px;
    border-radius: 50%;
    min-width: 12px;
    text-align: center;
    line-height: 1;
}

/* Empty State */
.receipt-items-empty {
    text-align: center;
    padding: 48px 16px;
    color: #6c757d;
}
.receipt-items-empty i {
    font-size: 48px;
    margin-bottom: 16px;
    color: #dee2e6;
}

/* --- Items Section Controls & Summary --- */
.receipt-items-controls { margin-bottom: 16px; }
.receipt-totals-summary {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 16px;
    margin-top: 16px;
}
.receipt-totals-summary .row {
    margin-bottom: 8px;
    align-items: center;
}
.receipt-totals-summary .row:last-child {
    margin-bottom: 0;
    padding-top: 8px;
    border-top: 2px solid #dee2e6;
    font-weight: bold;
    font-size: 18px;
}
.receipt-totals-summary .col-6:first-child {
    text-align: right;
    font-weight: 600;
}
.receipt-totals-summary .col-6:last-child {
    font-family: 'Courier New', monospace;
    font-weight: bold;
}

/* --- Animations & UI States --- */
@keyframes receiptFadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}
.receipt-fade-in { animation: receiptFadeIn 0.5s ease-in-out; }

@keyframes slideInOut {
    from { opacity: 0; transform: translateY(-20px); }
    to { opacity: 1; transform: translateY(0); }
}
.receipt-item.item-adding { animation: slideInOut 0.3s ease-out; }
.receipt-item.item-removing { animation: slideInOut 0.3s ease-in reverse; }

.receipt-item-highlight {
    background-color: #fff3cd !important;
    transition: background-color 0.3s ease;
}
.receipt-status {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}
.receipt-status.paid { background-color: #d1edff; color: #0c5460; }
.receipt-status.pending { background-color: #fff3cd; color: #856404; }
.receipt-status.cancelled { background-color: #f8d7da; color: #721c24; }

/* --- Responsive Design --- */
@media (max-width: 768px) {
    .receipt { padding: 16px; font-size: 14px; }
    .receipt-header .company-name { font-size: 24px; }
    .receipt-info { flex-direction: column; gap: 16px; }
    .receipt-info .right { text-align: left; }
    .items-table { font-size: 13px; }
    .items-table th, .items-table td { padding: 8px 4px; }
    .totals-table { max-width: 250px; }
    .payment-info { flex-direction: column; gap: 8px; text-align: center; }
    /* Items Management Responsive */
    .receipt-item .row { flex-direction: column; align-items: stretch; }
    .receipt-item .col-md-4, .receipt-item .col-md-2 { width: 100%; margin-bottom: 8px; }
    .receipt-item .col-md-2:last-child { margin-bottom: 0; }
    .drag-handle {
        position: static;
        transform: none;
        align-self: flex-start;
        margin-bottom: 8px;
        margin-right: 8px;
        display: inline-block;
    }
    .receipt-item.draggable {
        padding-left: 12px;
        flex-direction: row;
        align-items: flex-start;
    }
    .receipt-item.draggable .row {
        flex: 1;
        margin-left: 8px;
    }
}

@media (max-width: 576px) {
    .receipt { padding: 12px; font-size: 14px; }
    .receipt-header .company-name { font-size: 21px; }
    .items-table { font-size: 12px; }
    .customer-info .info-row { flex-direction: column; }
    .customer-info .info-label { width: auto; margin-bottom: 2px; }
}