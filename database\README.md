# KMS PC Receipt Maker - Database Structure

## 資料庫組織結構

資料庫按功能模組化組織，避免重複並提高維護性：

### 檔案結構

1. **`01_core_tables.sql`** - 核心表格
   - `receipts` - 收據主表
   - `receipt_items` - 收據項目表

2. **`02_product_management.sql`** - 產品管理
   - `pc_parts` - 電腦零件預設表

3. **`03_configuration_tables.sql`** - 配置表格
   - `receipt_configurations` - 收據配置表

4. **`04_sample_data.sql`** - 範例數據
   - 預設電腦零件數據

5. **`setup_organized.sql`** - 主設置腳本
   - 執行所有模組的統一入口

6. **`setup.sql`** - 原始設置腳本（保留向後相容性）

### 使用方法

#### 方法一：使用組織化腳本
```sql
-- 在 MySQL 中執行
SOURCE database/setup_organized.sql;
```

#### 方法二：分別執行各模組
```sql
SOURCE database/01_core_tables.sql;
SOURCE database/02_product_management.sql;
SOURCE database/03_configuration_tables.sql;
SOURCE database/04_sample_data.sql;
```

#### 方法三：使用原始腳本（向後相容）
```sql
SOURCE database/setup.sql;
```

### 表格關係

```
receipts (收據主表)
├── receipt_items (收據項目) [FK: receipt_id]
└── receipt_configurations (收據配置)

pc_parts (電腦零件預設)
└── 獨立表格，供前端選擇使用
```

### 維護指南

- **新增功能表格**：創建新的 SQL 檔案，按功能分類
- **修改現有表格**：在對應的功能模組檔案中修改
- **避免重複**：確保每個表格只在一個檔案中定義
- **保持相容性**：重要變更時保留原始 `setup.sql` 檔案
